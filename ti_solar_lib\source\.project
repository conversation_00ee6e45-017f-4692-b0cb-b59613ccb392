<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>Solar_Lib_Float</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>rts2800_fpu32_fast_supplement.lib</name>
			<type>1</type>
			<locationURI>PARENT-5-PROJECT_LOC/math/FPUfastRTS/V100/lib/rts2800_fpu32_fast_supplement.lib</locationURI>
		</link>
		<link>
			<name>Debug/Solar_Lib_Float.lib</name>
			<type>1</type>
			<locationURI>EXTERNAL_BUILD_ARTIFACT</locationURI>
		</link>
	</linkedResources>
</projectDescription>

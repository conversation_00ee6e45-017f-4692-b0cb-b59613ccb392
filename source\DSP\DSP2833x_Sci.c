// TI File $Revision: /main/2 $
// Checkin $Date: March 1, 2007   16:06:07 $
//###########################################################################
//
// FILE:	DSP2833x_Sci.c
//
// TITLE:	DSP2833x SCI Initialization & Support Functions.
//
//###########################################################################
// $TI Release: 2833x/2823x Header Files V1.32 $
// $Release Date: June 28, 2010 $
// $Copyright:
// Copyright (C) 2009-2021 Texas Instruments Incorporated - http://www.ti.com/
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//###########################################################################

//
// Included Files
//
#include "DSP2833x_Device.h"     // DSP2833x Headerfile Include File
#include "DSP2833x_Examples.h"   // DSP2833x Examples Include File
#include "SC_master.h"

static SciStruct                    SciList[MAX_SCI_NO];
static QUEUE                        QList[MAX_SCI_NO];
static Uint8                        szSciRxBuf[MAX_SCI_BUF_SIZE];
//static volatile struct SCI_REGS     *pSciRegsIndex[MAX_SCI_NO] = {&SciaRegs,&ScibRegs,&ScicRegs};

/**********************************************************************
* FUNCION :  InitQueue
* PURPOSE :
* CALLED BY:
**********************************************************************/
static void InitQueue(QUEUE *pQue, Uint8 *pu8Start, Uint16 u16BufSize)
{
    pQue->u16Length = 0;
    pQue->u16Size = u16BufSize;
    pQue->pu8In = pu8Start;
    pQue->pu8Out = pu8Start;
    pQue->pu8Start = pu8Start;
}
/**********************************************************************
* FUNCION :  QueDataIn
* PURPOSE :
* CALLED BY:
**********************************************************************/
Uint16 QueDataIn(QUEUE *pQue, Uint16 u16QueData)
{
    if(pQue->u16Length == pQue->u16Size)
    {
        if(pQue->pu8In == pQue->pu8Start)
        {
            *(pQue->pu8Start + pQue->u16Size - 1) = u16QueData;
        }
        else
        {
            *(pQue->pu8In - 1) = u16QueData;
        }
        return QUE_BUF_FULL;
    }
    else
    {
        *(pQue->pu8In) = u16QueData;
        pQue->u16Length += 1;
        if(pQue->pu8In == pQue->pu8Start + pQue->u16Size - 1)
        {
            pQue->pu8In = pQue->pu8Start;
        }
        else
        {
            pQue->pu8In += 1;
        }
        return QUE_BUF_NORMAL;
    }
}
/**********************************************************************
* FUNCION :  QueDataOut
* PURPOSE :
* CALLED BY:
**********************************************************************/
//static Uint16 QueDataOut(QUEUE *pQue, Uint8 *pQueData)
//{
//    if(0 == pQue->u16Length)
//    {
//        return QUE_BUF_EMPTY;
//    }
//    else
//    {
//        *pQueData = *(pQue->pu8Out);
//        pQue->u16Length -= 1;
//        if(pQue->pu8Out == (pQue->pu8Start + pQue->u16Size - 1))
//        {
//            pQue->pu8Out = pQue->pu8Start;
//        }
//        else
//        {
//            pQue->pu8Out += 1;
//        }
//        return QUE_BUF_NORMAL;
//    }
//}
/**********************************************************************
* FUNCION :  SetScia
* PURPOSE :  This function initializes the SCIa data structure.
* CALLED BY:
**********************************************************************/
static void SetScia(void)
{
    SciStruct   *pSci;
    QUEUE       *pq;
    Uint8       *pSCIBuf;

    pSci = &SciList[ID_SCIA];
    g_pSciIndex[ID_SCIA] = pSci;

    pSci->pqRx = &QList[ID_SCIA];
    pq = pSci->pqRx;
    pSCIBuf = szSciRxBuf;
    InitQueue(pq, pSCIBuf, SCIA_BUF_SIZE);

    pSci->u8TxStatus = SCI_TX_RDY;
    pSci->u16TxLength = 0;

}
/**********************************************************************
* FUNCION :  SetScib
* PURPOSE :  This function initializes the SCIb data structure.
* CALLED BY:
**********************************************************************/
static void SetScib(void)
{
    SciStruct   *pSci;
    QUEUE       *pq;
    Uint8       *pSCIBuf;

    pSci = &SciList[ID_SCIB];
    g_pSciIndex[ID_SCIB] = pSci;

    pSci->pqRx = &QList[ID_SCIB];
    pq = pSci->pqRx;
    pSCIBuf = szSciRxBuf + SCIA_BUF_SIZE;
    InitQueue(pq, pSCIBuf, SCIB_BUF_SIZE);

    pSci->u8TxStatus = SCI_TX_RDY;
    pSci->u16TxLength = 0;
}

/**********************************************************************
* FUNCION :  InitScia
* PURPOSE :  This function initializes the SCIa to a known state.
* CALLED BY:
**********************************************************************/
void InitScia(Uint32 u32BaudRate, Uint16 u16RxMode)
{
    Uint16 u16Br;

    // Initialize SCI-A:
    SciaRegs.SCICCR.all = 0x0007;           // 1 stop bit, No loopback, No parity, async mode,
                                            // idle-line protocol, 8 char bits,
    SciaRegs.SCICTL1.all = 0x0003;          // RX_err_INT disable, sleepMode disable, RX/TX enable
                                            // Disable RX ERR, SLEEP, TXWAKE
    SciaRegs.SCICTL2.bit.TXINTENA = 1;      // 0 = disable, 1 = enable TXRDY INT, in FIFO, this INT used as FIFO_Int
    SciaRegs.SCICTL2.bit.RXBKINTENA = 1;    // 1 = enable RXrdy/BRKINT, 0 = disable
    SciaRegs.SCIFFTX.all = 0xE040;
    SciaRegs.SCIFFCT.all = 0x00;
    SciaRegs.SCICTL1.all = 0x0023;          // Relinquish SCI from Reset, by software reset
    SciaRegs.SCIFFTX.bit.TXFIFOXRESET = 1;  // enable TX
    SciaRegs.SCIFFRX.bit.RXFIFORESET = 1;   // enable RX
    SciaRegs.SCIPRI.bit.FREE = 1;           // free run enabled, 0 = soft emulation suspend, 1 = free run,

    u16Br = ((LSPCLK_FREQ/u32BaudRate)/8)-1;
    SciaRegs.SCIHBAUD = GET_HBYTE_OF_WORD(u16Br);   // BRR setting, H byte, when BaudRate=9600. brr=0x01E7
    SciaRegs.SCILBAUD = GET_LBYTE_OF_WORD(u16Br);   // BRR setting, L byte

    if(MODE_INT == u16RxMode)
    {
        SciaRegs.SCIFFRX.all = 0x6061;
        PieCtrlRegs.PIEIER9.bit.INTx1 = 1;      // Enable SCIA_RXINT in PIE group 9
        IER |= M_INT9;                          // Enable INT9 in IER to enable PIE group 9
    }
    else
    {
        SciaRegs.SCIFFRX.all = 0x2041;
    }

    SetScia();
}
/**********************************************************************
* FUNCION :  InitScib
* PURPOSE :  This function initializes the SCIb to a known state.
* CALLED BY:
**********************************************************************/
void InitScib(Uint32 u32BaudRate, Uint16 u16RxMode)
{
    Uint16 u16Br;

    // Initialize SCI-B:
    ScibRegs.SCICCR.all = 0x0007;           // 1 stop bit, No loopback, No parity, async mode,
                                            // idle-line protocol, 8 char bits,
    ScibRegs.SCICTL1.all = 0x0003;          // RX_err_INT disable, sleepMode disable, RX/TX enable
                                            // Disable RX ERR, SLEEP, TXWAKE
    ScibRegs.SCICTL2.bit.TXINTENA = 1;      // 0 = disable, 1 = enable TXRDY INT, in FIFO, this INT used as FIFO_Int
    ScibRegs.SCICTL2.bit.RXBKINTENA = 1;    // 1 = enable RXrdy/BRKINT, 0 = disable
    ScibRegs.SCIFFTX.all = 0xE040;
    ScibRegs.SCIFFCT.all = 0x00;
    ScibRegs.SCICTL1.all = 0x0023;          // Relinquish SCI from Reset, by software reset
    ScibRegs.SCIFFTX.bit.TXFIFOXRESET = 1;  // enable TX
    ScibRegs.SCIFFRX.bit.RXFIFORESET = 1;   // enable RX
    ScibRegs.SCIPRI.bit.FREE = 1;           // free run enabled, 0 = soft emulation suspend, 1 = free run,

    u16Br = ((LSPCLK_FREQ/u32BaudRate)/8)-1;
    ScibRegs.SCIHBAUD = GET_HBYTE_OF_WORD(u16Br);   // BRR setting, H byte, when BaudRate=9600. brr=0x01E7
    ScibRegs.SCILBAUD = GET_LBYTE_OF_WORD(u16Br);   // BRR setting, L byte

    if(MODE_INT == u16RxMode)
    {
        ScibRegs.SCIFFRX.all = 0x6061;
        PieCtrlRegs.PIEIER9.bit.INTx3 = 1;      // Enable SCIB_RXINT in PIE group 9
        IER |= M_INT9;                          // Enable INT9 in IER to enable PIE group 9
    }
    else
    {
        ScibRegs.SCIFFRX.all = 0x2041;
    }

    SetScib();
}
//
// InitSci - This function initializes the SCI(s) to a known state.
//
/*void
InitSci(void)
{
    //
    // Initialize SCI-A
    //

    //
    // Initialize SCI-B
    //

    //
    // Initialize SCI-C
    //
}*/
void InitSci(void)
{
    g_pSciIndex[ID_SCIA] = NULL;
    g_pSciIndex[ID_SCIB] = NULL;
    g_pSciIndex[ID_SCIC] = NULL;

#ifdef  _IPOMS_
    InitScia(IPOMS_COMM_BAUDRATE, MODE_INT);
#endif
    InitScib(SCI_UPDATE_COMM_BAUDRATE, MODE_INT);
}
//
// InitSciGpio - This function initializes GPIO to function as SCI-A, SCI-B, or
// SCI-C
//
// Each GPIO pin can be configured as a GPIO pin or up to 3 different
// peripheral functional pins. By default all pins come up as GPIO
// inputs after reset.  
// 
// Caution: 
// Only one GPIO pin should be enabled for SCITXDA/B operation.
// Only one GPIO pin shoudl be enabled for SCIRXDA/B operation. 
// Comment out other unwanted lines.
//
void 
InitSciGpio()
{
    InitSciaGpio();
#if DSP28_SCIB   
    InitScibGpio();
#endif // if DSP28_SCIB  

#if DSP28_SCIC
    InitScicGpio();
#endif // if DSP28_SCIC
}

//
// InitSciaGpio - This function initializes GPIO pins to function as SCI-A pins
//
void 
InitSciaGpio()
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled disabled by the user.  
    // This will enable the pullups for the specified pins.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO28 = 0;  // Enable pull-up for GPIO28 (SCIRXDA)
    GpioCtrlRegs.GPAPUD.bit.GPIO29 = 0;	 // Enable pull-up for GPIO29 (SCITXDA)

    //
    // Set qualification for selected pins to asynch only
    // Inputs are synchronized to SYSCLKOUT by default.  
    // This will select asynch (no qualification) for the selected pins.
    //
    GpioCtrlRegs.GPAQSEL2.bit.GPIO28 = 3;  // Asynch input GPIO28 (SCIRXDA)
    
    //
    // Configure SCI-A pins using GPIO regs
    // This specifies which of the possible GPIO pins will be SCI functional
    // pins.
    //
    GpioCtrlRegs.GPAMUX2.bit.GPIO28 = 1;   // Configure GPIO28 to SCIRXDA 
    GpioCtrlRegs.GPAMUX2.bit.GPIO29 = 1;   // Configure GPIO29 to SCITXDA 

    EDIS;
}

#if DSP28_SCIB
//
// InitScibGpio - This function initializes GPIO pins to function as SCI-B pins
//
void 
InitScibGpio()
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins 
    // Pull-ups can be enabled or disabled disabled by the user.  
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAPUD.bit.GPIO9 = 0;  //Enable pull-up for GPIO9  (SCITXDB)
    //GpioCtrlRegs.GPAPUD.bit.GPIO14 = 0; //Enable pull-up for GPIO14 (SCITXDB)
//    GpioCtrlRegs.GPAPUD.bit.GPIO18 = 0;	 //Enable pull-up for GPIO18 (SCITXDB)
    GpioCtrlRegs.GPAPUD.bit.GPIO22 = 0; //Enable pull-up for GPIO22 (SCITXDB)

    //GpioCtrlRegs.GPAPUD.bit.GPIO11 = 0; //Enable pull-up for GPIO11 (SCIRXDB)
    //GpioCtrlRegs.GPAPUD.bit.GPIO15 = 0; //Enable pull-up for GPIO15 (SCIRXDB)
//    GpioCtrlRegs.GPAPUD.bit.GPIO19 = 0;	 //Enable pull-up for GPIO19 (SCIRXDB)
    GpioCtrlRegs.GPAPUD.bit.GPIO23 = 0; //Enable pull-up for GPIO23 (SCIRXDB)

    //
    // Set qualification for selected pins to asynch only
    // This will select asynch (no qualification) for the selected pins.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAQSEL1.bit.GPIO11 = 3;  // Asynch input GPIO11 (SCIRXDB)
    //GpioCtrlRegs.GPAQSEL1.bit.GPIO15 = 3;  // Asynch input GPIO15 (SCIRXDB)
//    GpioCtrlRegs.GPAQSEL2.bit.GPIO19 = 3;  // Asynch input GPIO19 (SCIRXDB)
    GpioCtrlRegs.GPAQSEL2.bit.GPIO23 = 3;  // Asynch input GPIO23 (SCIRXDB)

    //
    // Configure SCI-B pins using GPIO regs
    // This specifies which of the possible GPIO pins will be SCI functional 
    // pins.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAMUX1.bit.GPIO9 = 2;  //Configure GPIO9 to SCITXDB 
    //GpioCtrlRegs.GPAMUX1.bit.GPIO14 = 2; //Configure GPIO14 to SCITXDB
//    GpioCtrlRegs.GPAMUX2.bit.GPIO18 = 2;  //Configure GPIO18 to SCITXDB
    GpioCtrlRegs.GPAMUX2.bit.GPIO22 = 3; //Configure GPIO22 to SCITXDB

    //GpioCtrlRegs.GPAMUX1.bit.GPIO11 = 2;  //Configure GPIO11 for SCIRXDB
    //GpioCtrlRegs.GPAMUX1.bit.GPIO15 = 2;  //Configure GPIO15 for SCIRXDB
//    GpioCtrlRegs.GPAMUX2.bit.GPIO19 = 2;   //Configure GPIO19 for SCIRXDB
    GpioCtrlRegs.GPAMUX2.bit.GPIO23 = 3;  //Configure GPIO23 for SCIRXDB

    EDIS;
}
#endif // if DSP28_SCIB 

#if DSP28_SCIC
//
// InitScicGpio - This function initializes GPIO pins to function as SCI-C pins
//
void 
InitScicGpio()
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled disabled by the user.  
    // This will enable the pullups for the specified pins.
    //
    GpioCtrlRegs.GPBPUD.bit.GPIO62 = 0;  // Enable pull-up for GPIO62 (SCIRXDC)
    GpioCtrlRegs.GPBPUD.bit.GPIO63 = 0;	 // Enable pull-up for GPIO63 (SCITXDC)

    //
    // Set qualification for selected pins to asynch only
    // Inputs are synchronized to SYSCLKOUT by default.  
    // This will select asynch (no qualification) for the selected pins.
    //
    GpioCtrlRegs.GPBQSEL2.bit.GPIO62 = 3;  // Asynch input GPIO62 (SCIRXDC)

    //
    // Configure SCI-C pins using GPIO regs
    // This specifies which of the possible GPIO pins will be SCI functional 
    // pins.
    //
    GpioCtrlRegs.GPBMUX2.bit.GPIO62 = 1;   // Configure GPIO62 to SCIRXDC
    GpioCtrlRegs.GPBMUX2.bit.GPIO63 = 1;   // Configure GPIO63 to SCITXDC

    EDIS;
}
#endif // if DSP28_SCIC 
	
//
// End of file
//


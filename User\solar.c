/*
 * solar.c - 太阳能逆变器主控制程序
 *
 * 主要功能模块:
 * 1. 电网电压和电流采样处理
 * 2. MPPT最大功率点跟踪算法
 * 3. 锁相环(SPLL)电网同步控制
 * 4. PWM逆变器输出控制
 * 5. 多级保护和故障检测
 * 6. 系统状态管理
 *
 * 中断服务程序架构:
 * - 50us高频中断: 采样处理、保护检测、PWM输出
 * - 1ms中频中断: RMS计算、状态管理、MPPT
 * - 主循环: 通信处理、MPPT算法
 */

#include "solar.h"

// ========== 系统参数定义 ==========
#define ISR_FREQUENCY (20000.0f)                           // 中断频率 20kHz (50us周期)
#define GRID_FREQ (50.0f)                                  // 电网基准频率 50Hz
#define RATED_VOLTAGE (220.0f)                             // 额定电压 220V RMS
#define RATED_POWER (10000.0f)                             // 额定功率 10kW
#define RATED_CURRENT (RATED_POWER / RATED_VOLTAGE / 3.0f) // 额定电流 15.15A RMS
#define CARRIER_MODULATION_RATIO (0.9f)                    // PWM载波调制比 (0.9表示90%调制深度)

// ========== 全局句柄变量 ==========
solar_handle_t h; // 太阳能逆变器主控制句柄

// ========== 调试功能模块 ==========
#if SOLAR_DEBUG
debug_handle_t hdebug; // 调试功能句柄

/**
 * @brief 调试功能初始化
 *
 * 功能: 初始化数据记录器和仿真功能模块
 * 用途: 用于开发调试和波形分析
 */
void solar_debug_init(void)
{
    memset(&hdebug, 0, sizeof(debug_handle_t)); // 清零调试句柄

    // ========== 数据记录器初始化 ==========
    DLOG_4CH_F_init(&hdebug.hdlog1);              // 初始化4通道数据记录器
    hdebug.hdlog1.pre_scalar = 1;                 // 预分频系数 (1表示不分频)
    hdebug.hdlog1.trig_value = 3.14159265f;       // 触发值 (π弧度，用于相位触发)
    hdebug.hdlog1.size = 100;                     // 记录缓冲区大小
    hdebug.hdlog1.status = 2;                     // 记录器状态 (2=自动触发模式)

    // 配置数据记录输入指针
    hdebug.hdlog1.input_ptr1 = &hdebug.dlog_get[0]; // 通道1输入数据指针
    hdebug.hdlog1.input_ptr2 = &hdebug.dlog_get[1]; // 通道2输入数据指针
    hdebug.hdlog1.input_ptr3 = &hdebug.dlog_get[2]; // 通道3输入数据指针
    hdebug.hdlog1.input_ptr4 = &hdebug.dlog_get[3]; // 通道4输入数据指针

    // 配置数据记录输出缓冲区
    hdebug.hdlog1.output_ptr1 = hdebug.dlog_buff[0]; // 通道1输出缓冲区指针
    hdebug.hdlog1.output_ptr2 = hdebug.dlog_buff[1]; // 通道2输出缓冲区指针
    hdebug.hdlog1.output_ptr3 = hdebug.dlog_buff[2]; // 通道3输出缓冲区指针
    hdebug.hdlog1.output_ptr4 = hdebug.dlog_buff[3]; // 通道4输出缓冲区指针

    // ========== 电网仿真功能初始化 ==========
    hdebug.simulation_sw = 1;                      // 仿真开关 (1=启用仿真功能)
    DQ0_ABC_F_init(&hdebug.simulation_grid);       // 初始化电网仿真DQ变换
    hdebug.simulation_theta = 0.0f;                // 仿真相位角初值
    hdebug.simulation_freq = 50.1f;                // 仿真电网频率 50.1Hz

    hdebug.cntl_value = 0; // 控制调试参数初值
}
#endif

/**
 * @brief 系统软复位功能
 *
 * 功能: 重新初始化所有控制模块和状态变量
 * 调用时机: 系统启动或故障恢复时
 */
void solar_reset(void)
{
    // ========== 控制算法模块初始化 ==========
    solar_mppt_init();  // 初始化MPPT最大功率点跟踪
    solar_cntl_init();  // 初始化电流环控制器
    cntl_pfc_init();    // 初始化功率因数校正控制器

    // ========== PV升压电路初始化 ==========
    PVBoost_Init(&h.hBoost); // 初始化PV升压控制器

    // ========== 锁相环和坐标变换初始化 ==========
    solar_spll_init();                    // 初始化锁相环模块
    DQ0_ABC_F_init(&h.hV_DQ0_ABC);       // 初始化电压DQ0-ABC坐标变换

    // ========== 母线平衡控制初始化 ==========
    h.cntl_bus_balance_compensation = 0; // 母线平衡补偿量清零

    // ========== 使能标志位初始化 ==========
    h.hBoost.is_enable = 0;        // 禁用PV升压电路
    h.is_output_epwm_enable = 0;   // 禁用PWM输出
    h.is_amp_sync_ok = 0;          // 清除幅值同步标志

    // ========== 状态机初始化 ==========
    h.state = HSTATE_START; // 设置为启动状态

    // ========== 增益校正系数初始化 ==========
    h.Ia_gain = 1; // A相电流增益校正
    h.Ib_gain = 1; // B相电流增益校正
    h.Ic_gain = 1; // C相电流增益校正
    h.LC_gain = 0; // LC滤波器增益校正
}
/**
 * @brief 太阳能逆变器系统初始化
 *
 * 功能: 完成整个系统的初始化配置
 * 包括: 采样模块、保护模块、控制参数等
 * 调用时机: 系统上电启动时
 */
void solar_Init(void)
{
#if SOLAR_DEBUG
    solar_debug_init(); // 初始化调试功能
#endif

    memset(&h, 0x00, sizeof(solar_handle_t)); // 清零主控制句柄

    solar_reset(); // 执行软复位

    // ========== 系统参数和采样模块配置 ==========
    {
        h.Ctrl_Power_SW = 1; // 使能功率控制

        // 设置数据包头标识 "SDH:"
        h.data.data_head |= ('S' << 0) | ('D' << 8) | ((uint32_t) ('H' << 0) | (':' << 8)) << 16;

        // ========== 电网电压采样模块初始化 ==========
        // 电网A相电压采样 (峰值311V基准, 电压传感器比例系数)
        Sample_Membe_Init(&h.Vgrid_a, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 212.0f)), 0);
        // 电网B相电压采样
        Sample_Membe_Init(&h.Vgrid_b, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 212.0f)), 0);
        // 电网C相电压采样
        Sample_Membe_Init(&h.Vgrid_c, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 213.0f)), 0);

        // ========== 逆变器输出电压采样模块初始化 ==========
        // 逆变器A相输出电压采样 (用于幅值同步控制)
        Sample_Membe_Init(&h.Vinv_a, RATED_VOLTAGE * 1.414f, ((0.6 * 159.0 / 179.0) * 1.0485), 0);
        // 逆变器B相输出电压采样
        Sample_Membe_Init(&h.Vinv_b, RATED_VOLTAGE * 1.414f, ((0.6 * 159.0 / 179.0) * 1.0485), 0);
        // 逆变器C相输出电压采样
        Sample_Membe_Init(&h.Vinv_c, RATED_VOLTAGE * 1.414f, ((0.6 * 159.0 / 181.0) * 1.0485), 0);

        // ========== 逆变器输出电流采样模块初始化 ==========
        // A相输出电流采样 (峰值21.4A基准, 电流传感器0.06V/A)
        Sample_Membe_Init(&h.Ia, RATED_CURRENT * 1.414f, 0.06, -0.0);
        // B相输出电流采样
        Sample_Membe_Init(&h.Ib, RATED_CURRENT * 1.414f, 0.06, -0.0);
        // C相输出电流采样
        Sample_Membe_Init(&h.Ic, RATED_CURRENT * 1.414f, 0.06, -0.0);

        // ========== 直流量采样模块初始化 ==========
        Sample_DC_Init(&h.Ipv1, 0.0025, 0);                    // PV1电流采样 (传感器2.5mV/A)
        Sample_DC_Init(&h.Ipv2, 0.0025, 0);                    // PV2电流采样
        Sample_DC_Init(&h.Vpv1, 0.25 * 1.10311750599520, 0);   // PV1电压采样 (分压比0.25V/V, 校正系数)
        Sample_DC_Init(&h.Vpv2, 0.25 * 1.10311750599520, 0);   // PV2电压采样
        Sample_DC_Init(&h.Vbus_pos, 0.1376, 0);                // 正母线电压采样 (分压比0.1376V/V)
        Sample_DC_Init(&h.Vbus_neg, 0.1376, 0);                // 负母线电压采样

        // ========== 快速保护模块初始化 (50us级保护) ==========
        // A相电流过流保护 (限值30A, 立即触发)
        Protect_Member_Init(&h.Protect_Ia, 30, -30, 0, 10000.0 * 20, PROT_Ia_Over, PROT_Ia_Over, "Ia_Over", "Ia_Under");
        // B相电流过流保护
        Protect_Member_Init(&h.Protect_Ib, 30, -30, 0, 10000.0 * 20, PROT_Ib_Over, PROT_Ib_Over, "Ib_Over", "Ib_Under");
        // C相电流过流保护
        Protect_Member_Init(&h.Protect_Ic, 30, -30, 0, 10000.0 * 20, PROT_Ic_Over, PROT_Ic_Over, "Ic_Over", "Ic_Under");
        // PV1电流过流保护 (10A限值, 立即触发)
        Protect_Member_Init(&h.Protect_Ipv1, 10, -999, 0, 10000.0 * 20, PROT_Ipv1_Over, PROT_Ipv1_Over, "Ipv1_Over", "Ipv1_Under");
        // PV2电流过流保护
        Protect_Member_Init(&h.Protect_Ipv2, 10, -999, 0, 10000.0 * 20, PROT_Ipv2_Over, PROT_Ipv2_Over, "Ipv2_Over", "Ipv2_Under");

        // ========== 延时保护模块初始化 (毫秒级延时保护) ==========
        // A相电流延时保护 (限值20A, 2ms延时)
        Protect_Member_Init(&h.Protect_Ia_II, 20, -20, 2.0, 10000.0 * 20, PROT_Ia_Over, PROT_Ia_Over, "Ia_Over_II", "Ia_Under_II");
        // B相电流延时保护
        Protect_Member_Init(&h.Protect_Ib_II, 20, -20, 2.0, 10000.0 * 20, PROT_Ib_Over, PROT_Ib_Over, "Ib_Over_II", "Ib_Under_II");
        // C相电流延时保护
        Protect_Member_Init(&h.Protect_Ic_II, 20, -20, 2.0, 10000.0 * 20, PROT_Ic_Over, PROT_Ic_Over, "Ic_Over_II", "Ic_Under_II");
        // PV1电流延时保护 (10A限值, 1ms延时)
        Protect_Member_Init(&h.Protect_Ipv1_II, 10, -999, 1.0, 10000.0 * 20, PROT_Ipv1_Over, PROT_Ipv1_Over, "Ipv1_Over_II", "Ipv1_Under_II");
        // PV2电流延时保护
        Protect_Member_Init(&h.Protect_Ipv2_II, 10, -999, 1.0, 10000.0 * 20, PROT_Ipv2_Over, PROT_Ipv2_Over, "Ipv2_Over_II", "Ipv2_Under_II");

        // ========== 电网参数保护模块初始化 ==========
        // 电网频率保护 (49.5-50.5Hz, 180ms延时)
        Protect_Member_Init(&h.Protect_Grid_Freq, 50.5, 49.5, 180.0, 10000.0, PROT_Grid_Freq_Over, PROT_Grid_Freq_Under, "Grid_Freq_Over", "Grid_Freq_Under");
        // 电网A相电压保护 (183-246V, 100ms延时)
        Protect_Member_Init(&h.Protect_Grid_Ua, RATED_VOLTAGE * 1.12, RATED_VOLTAGE * 0.83, 100.0, 10000.0, PROT_Grid_Ua_Over, PROT_Grid_Ua_Under, "Grid_Ua_Over", "Grid_Ua_Under");
        // 电网B相电压保护
        Protect_Member_Init(&h.Protect_Grid_Ub, RATED_VOLTAGE * 1.12, RATED_VOLTAGE * 0.83, 100.0, 10000.0, PROT_Grid_Ub_Over, PROT_Grid_Ub_Under, "Grid_Ub_Over", "Grid_Ub_Under");
        // 电网C相电压保护
        Protect_Member_Init(&h.Protect_Grid_Uc, RATED_VOLTAGE * 1.12, RATED_VOLTAGE * 0.83, 100.0, 10000.0, PROT_Grid_Uc_Over, PROT_Grid_Uc_Under, "Grid_Uc_Over", "Grid_Uc_Under");

        // ========== 直流电压保护模块初始化 ==========
        // PV1电压保护 (270-700V, 1ms延时)
        Protect_Member_Init(&h.Protect_Vpv1, 700, 270, 1.0, 10000.0, PROT_Vpv1_Over, PROT_Vpv1_Under, "Vpv1_Over", "Vpv1_Under");
        // PV2电压保护
        Protect_Member_Init(&h.Protect_Vpv2, 700, 270, 1.0, 10000.0, PROT_Vpv2_Over, PROT_Vpv2_Under, "Vpv2_Over", "Vpv2_Under");
        // 母线不平衡保护 (差值100V, 180ms延时)
        Protect_Member_Init(&h.Protect_VBusBalance, 100, -100, 180.0, 10000.0, PROT_VBusBalance_Over, PROT_VBusBalance_Under, "VBusBalance_Over", "VBusBalance_Under");
        // 母线总电压保护 (276-830V, 10ms延时)
        Protect_Member_Init(&h.Protect_VBusSum, 830, 276, 10.0, 10000.0, PROT_VBusSum_Over, PROT_VBusSum_Under, "VBusSum_Over", "VBusSum_Under");

        h.Protect_Vpv1.ActFlag = PROT_DISABLE; // 暂时禁用PV1电压保护
    }
}

/**
 * @brief 检查单周期采样完成标志
 * @return uint16_t 1=完成, 0=未完成
 *
 * 功能: 检查电网单周期(20ms)采样是否完成
 */
uint16_t Sample_Completed_Check_1p(void)
{
    return h.Vgrid_a.flag_completed_1p;
}

/**
 * @brief 检查多周期采样完成标志
 * @return uint16_t 1=完成, 0=未完成
 *
 * 功能: 检查电网多周期采样统计是否完成
 */
uint16_t Sample_Completed_Check_xp(void)
{
    return h.Vgrid_a.flag_completed_xp;
}

/**
 * @brief 清除采样完成标志
 *
 * 功能: 清除所有采样完成标志位
 */
void Sample_Completed_Clear(void)
{
    h.Vgrid_a.flag_completed_1p = 0; // 清除单周期完成标志
    h.Vgrid_a.flag_completed_xp = 0; // 清除多周期完成标志
}

/**
 * @brief 50us高频中断服务程序
 *
 * 功能: 系统最高优先级实时控制任务
 * 中断频率: 20kHz (每50us执行一次)
 * 主要任务:
 * 1. ADC采样数据处理
 * 2. 快速保护检测
 * 3. 锁相环计算
 * 4. PWM波形输出
 */
void solar_ISR_50us(void)
{
    solar_Sampler();      // 执行ADC采样和信号调理
    solar_Protector_us(); // 执行微秒级快速保护

#if SOLAR_DEBUG

    //    hdebug.simulation_theta += ((float) (2.0f * M_PI) / 400.0f / 50.0f) * (hdebug.simulation_freq);
    //    if (hdebug.simulation_theta > ((float) (2.0f * M_PI)))
    //        hdebug.simulation_theta -= ((float) (2.0f * M_PI));
    //    else if (hdebug.simulation_theta < 0.0f)
    //        hdebug.simulation_theta += ((float) (2.0f * M_PI));
    //
    //    hdebug.simulation_grid.d = 311;
    //    //    hdebug.simulation_grid.q = 0;
    ////        hdebug.simulation_grid.sin = sinf(hdebug.simulation_theta);
    ////        hdebug.simulation_grid.cos = cosf(hdebug.simulation_theta);
    //    sincos(hdebug.simulation_theta, &hdebug.simulation_grid.sin, &hdebug.simulation_grid.cos);
    //    DQ0_ABC_F_MACRO(hdebug.simulation_grid);
    //
    //    hdebug.simulation_current.d = 10;
    //    //    hdebug.simulation_current.q = 0.0;
    ////        hdebug.simulation_current.sin = sinf(hdebug.simulation_theta);
    ////    hdebug.simulation_current.cos = cosf(hdebug.simulation_theta);
    //    sincos(hdebug.simulation_theta, &hdebug.simulation_current.sin, &hdebug.simulation_current.cos);
    //    DQ0_ABC_F_MACRO(hdebug.simulation_current);
    //
    //    if (hdebug.simulation_sw)
    //    {
    //        static float a = 0, b = 0, c = 0;
    //        a += (hdebug.simulation_grid.a - a) * (1.0f / 1.0f);
    //        b += (hdebug.simulation_grid.b - b) * (1.0f / 1.0f);
    //        c += (hdebug.simulation_grid.c - c) * (1.0f / 1.0f);
    //        h.Vgrid_a.out_instant = a;
    //        h.Vgrid_b.out_instant = b * 1.2;
    //        h.Vgrid_c.out_instant = c * 0.8;
    //
    //        h.Ia.out_instant = hdebug.simulation_current.a;
    //        h.Ib.out_instant = hdebug.simulation_current.b;
    //        h.Ic.out_instant = hdebug.simulation_current.c;
    //    }

    //        hdebug.dlog_get[0] = h.spll_theta;

    //    hdebug.dlog_get[0] = h.Grid_Ua.out_instant;
    //    hdebug.dlog_get[1] = h.Grid_Ub.out_instant; // fabsf(h.VBusPos.out_instant - h.VBusNeg.out_instant);

#endif

    solar_spll_calc(); // 执行锁相环和电网同步计算

    solar_output_epwm(); // 执行PWM波形生成和输出

    if (h.hProtect.protect_word[0] == 0) // 仅在无保护故障时记录调试数据
    {
        static uint16_t count = 0, i = 0;
        if (++count >= 4) // 每4个中断周期记录一次数据(降采样)
        {
            count = 0;
            {
                hdebug.data_buff[0][i] = h.spll_theta;                    // 电网锁相环相位角
                hdebug.data_buff[1][i] = h.hIspll_3ph_ddsrf.theta[1];     // 电流锁相环相位角

                hdebug.data_buff[2][i] = h.hSPLL_3ph_DDSRF.d_p_decoupl;   // 电网电压d轴分量
                hdebug.data_buff[3][i] = h.hSPLL_3ph_DDSRF.q_p_decoupl;   // 电网电压q轴分量

                hdebug.data_buff[4][i] = h.hIspll_3ph_ddsrf.d_p_decoupl;  // 输出电流d轴分量
                hdebug.data_buff[5][i] = h.hIspll_3ph_ddsrf.q_p_decoupl;  // 输出电流q轴分量

                hdebug.data_buff[6][i] = h.hCNTL_PI_d.Out;                // d轴电流控制器输出
                hdebug.data_buff[7][i] = h.Out_q;                         // q轴控制输出
            }
            if (++i >= 100) // 循环缓冲区索引
                i = 0;
        }
    }
}

/**
 * @brief 1ms中频中断服务程序
 *
 * 功能: 太阳能逆变器中频控制循环
 * 中断频率: 1kHz (每1ms执行一次)
 * 主要任务:
 * 1. AC有效值计算
 * 2. 毫秒级保护检测
 * 3. PV功率管理和MPPT算法
 * 4. 系统状态管理
 * 5. 数据记录处理
 */
void solar_ISR_1ms(void)
{
    solar_AC_RMS_Calc();  // 计算交流量有效值 (RMS)
    solar_Protector_ms(); // 执行毫秒级保护检测
    solar_PV_Manager();   // 执行PV功率管理和MPPT算法

    solar_StateManager(); // 执行系统状态管理

    // ========== 调试数据记录 ==========
    hdebug.dlog_get[0] = h.spll_theta;                               // 记录电网锁相环相位角
    hdebug.dlog_get[1] = h.spll_theta - h.hIspll_3ph_ddsrf.theta[1]; // 记录相位差
    hdebug.dlog_get[2] = h.Vgrid_a.out_instant;                     // 记录A相电网电压瞬时值
    hdebug.dlog_get[3] = (h.Vbus_pos.out_instant - h.Vbus_neg.out_instant); // 记录母线电压差
    DLOG_4CH_F_MACRO(hdebug.hdlog1); // 执行数据记录

    // ========== 电流环控制器计算 ==========
    {
        // D轴电流PI控制器
        h.hCNTL_PI_d.Ref = h.Id_lpf;                                // 设置D轴电流参考值
        h.hCNTL_PI_d.Fbk = h.hIspll_3ph_ddsrf.d_p_decoupl_lpf;    // 设置D轴电流反馈值
        CNTL_PI_F_MACRO(h.hCNTL_PI_d);                             // 执行D轴电流PI控制

        // 母线平衡控制器 (仅在有功电流足够大时执行)
        if (h.hIspll_3ph_ddsrf.d_p_decoupl_lpf > 0.05)
        {
            // 计算母线电压不平衡量作为反馈
            h.hCNTL_PI_BusBalance.Fbk = -(h.Vbus_pos.out_instant - h.Vbus_neg.out_instant) / h.Vbus;
            CNTL_PI_F_MACRO(h.hCNTL_PI_BusBalance);                // 执行母线平衡PI控制
        }
    }

    // ========== 单周期采样完成处理 ==========
    if (Sample_Completed_Check_1p())
    {
        static float grid_v[2]; // 电网电压历史值缓存

        grid_v[1] = grid_v[0]; // 保存上一次的电网电压
        // 计算三相电网电压平均值
        grid_v[0] = (h.Vgrid_a.out_rms_xp + h.Vgrid_b.out_rms_xp + h.Vgrid_c.out_rms_xp) / 3;

        // 电网电压突降检测 (已注释)
        //        if ((grid_v[0] - grid_v[1]) < -30)
        //        {
        //            h.hProtect.protect_code = 0;
        //            h.hProtect.text_label = "close boost";
        //            h.hProtect.protect_flag = 1;
        //        }
    }

    // ========== 多周期采样完成处理 ==========
    if (Sample_Completed_Check_xp())
    {
        // 计算各相输出功率 (电压乘电流)
        h.Output_Power_a_rms_xp = h.Vgrid_a.out_rms_xp * h.Ia.out_rms_xp;
        h.Output_Power_b_rms_xp = h.Vgrid_b.out_rms_xp * h.Ib.out_rms_xp;
        h.Output_Power_c_rms_xp = h.Vgrid_c.out_rms_xp * h.Ic.out_rms_xp;
        h.Output_Power_Total_rms_xp = h.Output_Power_a_rms_xp + h.Output_Power_b_rms_xp + h.Output_Power_c_rms_xp;
    }
    h.Input_Power_MPPT1 = 0; // PV1输入功率 (暂时禁用)
    h.Input_Power_MPPT2 = h.Ipv2.out_instant * h.Vpv2.out_instant; // PV2输入功率
    h.Input_Power_Total += (h.Input_Power_MPPT1 + h.Input_Power_MPPT2 - h.Input_Power_Total) * 0.01; // 总输入功率滤波

    if (SOLAR_GET_INTERCONNECTION()) // 仅在并网状态下执行功率控制
    {
        if (h.hBoost.power_ctlr < h.hBoost.power_target) // 功率爬坡控制
        {
            static uint32_t count = 0;
            if (++count >= 5000) // 每5秒执行一次大步长调整
            {
                count = 0;
                h.hBoost.power_ctlr = h.Input_Power_Total + (1000.0f / (60.0f * 1000.0f)) * 10;
            }
            else
            {
                h.hBoost.power_ctlr += (1000.0f / (60.0f * 1000.0f)); // 每分钟增加1kW
            }
        }
        else if (h.hBoost.power_ctlr > h.hBoost.power_target + 10)
        {
            h.hBoost.power_ctlr -= (10000.0f / (60.0f * 1000.0f)); // 每分钟减少10kW
        }

        if (h.Input_Power_Total > h.hBoost.power_ctlr + 10) // 功率限制保护
        {
            if (ECap2Regs.CAP4 > 10)
            {
                ECap2Regs.CAP4 -= 1; // 减小PWM占空比
            }
        }
    }

    h.time_tick++; // 系统时间计数器递增

    SCI_SR_TIM1ms(&hScia); // 串口A定时处理
    SCI_SR_TIM1ms(&hScib); // 串口B定时处理

    Sample_Completed_Clear(); // 清除采样完成标志
}

/**
 * @brief 系统主循环函数
 *
 * 功能: 处理低优先级任务和通信
 * 调用频率: 由主程序调度，非定时执行
 * 主要任务:
 * 1. 通信协议处理
 * 2. 功率开关状态检查
 * 3. PV功率不足检测
 */
void solar_Loop(void)
{
    solar_Communicator(); // 处理通信协议

    SCI_SR_Loop(&hScia); // 串口A循环处理
    SCI_SR_Loop(&hScib); // 串口B循环处理

    if (h.Ctrl_Power_SW == 0) // 功率开关关闭时的处理逻辑
    {
        if (h.state != HSTATE_STOP)
        {
            h.hBoost.power_target = 200; // 设置低功率目标

            if (h.Input_Power_MPPT2 < 300 || h.Vbus < h.hBoost.vbus_base)
            {
                protect_set_error_code(PROT_PowerOff, "PROT_PowerOff");
                h.state = HSTATE_STOP;
                protect_reset_error_code(PROT_PowerOff);
            }
        }
    }

    if (h.state == HSTATE_RUNING) // 运行状态下的功率监控
    {
        { // PV功率不足长期监控

            static uint32_t PV_underpower_tick = 0;
            if (h.Vbus < h.hBoost.vbus_base || h.Input_Power_MPPT1 < 300)
            {
                if (SOLAR_GET_TICK_COUNT(PV_underpower_tick) > 60000 * 10) // 10分钟超时
                {
                    PV_underpower_tick = 0;

                    // 可选择启用PV功率不足保护
                    //                    solar_set_protect_flag(PROT_PV_underpower);
                    //                    solar_handle.Ctrl_Power_SW = 0;
                }
            }
            else
            {
                PV_underpower_tick = SOLAR_GET_TICK(); // 重置计时器
            }
        }
    }
}

/**
 * @brief PV功率管理器
 *
 * 功能: 根据系统状态执行相应的PV控制策略
 * 调用时机: 1ms中断中调用
 */
void solar_PV_Manager(void)
{
    if (h.hBoost.is_enable) // 仅在升压电路使能时执行
    {
        if (h.state == HSTATE_SYNC) // 同步状态：执行幅值同步
        {
            solar_amp_sync(&h.hBoost);
        }
        else if (h.state == HSTATE_RUNING) // 运行状态：执行MPPT
        {
            if (h.Input_Power_Total < h.hBoost.power_ctlr)
            {
                solar_mppt_calc(); // 执行MPPT算法
            }
        }
    }
}

/**
 * @brief 逆变器输出幅值同步控制
 * @param hPV PV升压控制器句柄指针
 *
 * 功能: 调整逆变器输出电压幅值与电网电压匹配
 * 原理: 通过调节PWM占空比使逆变器输出电压接近电网电压
 */
void solar_amp_sync(PVBoost_Handle_t *hPV)
{
    if (h.is_amp_sync_ok == 0) // 幅值同步尚未完成
    {
        static float Vbus_avg = 460; // 母线电压滤波值

        Vbus_avg += (h.Vbus - Vbus_avg) * 0.01; // 母线电压低通滤波

        int PwmCap = ECap2Regs.CAP4; // 读取当前PWM占空比

        if (Sample_Completed_Check_xp()) // 多周期采样完成时执行
        {
            hPV->Vinv_avg = (h.Vinv_a.out_rms_xp + h.Vinv_b.out_rms_xp + h.Vinv_c.out_rms_xp) / 3; // 逆变器输出电压平均值
            hPV->Vgrid_avg = (h.Vgrid_a.out_rms_xp + h.Vgrid_b.out_rms_xp + h.Vgrid_c.out_rms_xp) / 3; // 电网电压平均值

            // 检查同步条件：电网电压正常、逆变器有输出、母线电压不过高
            if (hPV->Vgrid_avg > (220.0f * 0.6f) && hPV->Vinv_avg > h.Vbus * (0.2f) && h.Vbus < 810.0f)
            {
                hPV->amp_err = hPV->Vgrid_avg - hPV->Vinv_avg; // 计算电压幅值误差

                if (hPV->amp_err > 10.0f) // 逆变器输出电压过低
                {
                    PwmCap += 10; // 增大PWM占空比
                }
                else if (hPV->amp_err < -10.0f) // 逆变器输出电压过高
                {
                    PwmCap += -10; // 减小PWM占空比
                }
                else
                {
                    PwmCap += (int32_t) hPV->amp_err; // 比例调节
                }

                hPV->amp_err_state <<= 1; // 误差状态寄存器左移
                if (hPV->amp_err < hPV->amp_err_min && hPV->amp_err > -hPV->amp_err_min) // 误差在允许范围内
                {
                    hPV->amp_err_state |= 1; // 设置当前位为1
                    if (hPV->amp_err_state == 0xFFFFFFFF) // 连续32次误差都在范围内，同步完成
                    {
                        hPV->amp_err_state = 0;

                        hPV->vbus_base = Vbus_avg / h.hSPLL_3ph_DDSRF.d_p_decoupl_lpf; // 计算基准母线电压

                        hPV->pwm_base = PwmCap; // 保存基准PWM占空比

                        h.is_amp_sync_ok = 1; // 设置同步完成标志
                    }
                }
            }
        }

        if (PwmCap > h.hBoost.MaxCap) // PWM占空比上限保护
            PwmCap = h.hBoost.MaxCap;
        if (PwmCap < 0) // PWM占空比下限保护
            PwmCap = 0;

        ECap2Regs.CAP4 = PwmCap; // 更新PWM占空比寄存器
    }
}

/**
 * @brief ADC采样数据处理函数
 *
 * 功能: 读取ADC转换结果并进行信号调理
 * 调用时机: 50us中断中调用
 * 处理内容:
 * 1. 交流量采样(电网电压、逆变器电压、输出电流)
 * 2. 直流量采样(PV电压电流、母线电压)
 * 3. 信号滤波和标度变换
 */
void solar_Sampler(void)
{
    // ========== 交流量采样处理 ==========
    SAMPLE_AC_CALCULATE_MACRO(h.Vgrid_a, AdcRegs.ADCRESULT5);   // 电网A相电压
    SAMPLE_AC_CALCULATE_MACRO(h.Vgrid_b, AdcRegs.ADCRESULT7);   // 电网B相电压
    SAMPLE_AC_CALCULATE_MACRO(h.Vgrid_c, AdcRegs.ADCRESULT9);   // 电网C相电压
    SAMPLE_AC_CALCULATE_MACRO(h.Vinv_a, AdcRegs.ADCRESULT6);    // 逆变器A相输出电压
    SAMPLE_AC_CALCULATE_MACRO(h.Vinv_b, AdcRegs.ADCRESULT4);    // 逆变器B相输出电压
    SAMPLE_AC_CALCULATE_MACRO(h.Vinv_c, AdcRegs.ADCRESULT15);   // 逆变器C相输出电压
    SAMPLE_AC_CALCULATE_MACRO(h.Ia, AdcRegs.ADCRESULT14);       // A相输出电流
    SAMPLE_AC_CALCULATE_MACRO(h.Ib, AdcRegs.ADCRESULT12);       // B相输出电流
    SAMPLE_AC_CALCULATE_MACRO(h.Ic, AdcRegs.ADCRESULT13);       // C相输出电流

    // ========== 直流量采样处理 ==========
    SAMPLE_DC_CALCULATE_MACRO(h.Ipv1, AdcRegs.ADCRESULT10);     // PV1电流
    SAMPLE_DC_CALCULATE_MACRO(h.Ipv2, AdcRegs.ADCRESULT8);      // PV2电流
    SAMPLE_DC_CALCULATE_MACRO(h.Vpv1, AdcRegs.ADCRESULT2);      // PV1电压
    SAMPLE_DC_CALCULATE_MACRO(h.Vpv2, AdcRegs.ADCRESULT0);      // PV2电压
    SAMPLE_DC_CALCULATE_MACRO(h.Vbus_pos, AdcRegs.ADCRESULT1);  // 正母线电压
    SAMPLE_DC_CALCULATE_MACRO(h.Vbus_neg, AdcRegs.ADCRESULT3);  // 负母线电压

    h.Vbus = h.Vbus_pos.out_instant + h.Vbus_neg.out_instant;   // 计算总母线电压

    // ========== MPPT算法用的滤波处理 ==========
    h.hMPPT_PNO2.Ipv += (h.Ipv2.out_instant - h.hMPPT_PNO2.Ipv) * (1.0f / 400.0f); // PV2电流滤波
    h.hMPPT_PNO2.Vpv += (h.Vpv2.out_instant - h.hMPPT_PNO2.Vpv) * (1.0f / 400.0f); // PV2电压滤波
}

/**
 * @brief 交流量有效值(RMS)计算函数
 *
 * 功能: 计算所有交流信号的有效值
 * 调用时机: 1ms中断中调用
 * 计算内容:
 * 1. 电网三相电压有效值 (用于电网参数监测)
 * 2. 逆变器三相输出电压有效值 (用于幅值同步控制)
 * 3. 三相输出电流有效值 (用于功率计算和保护)
 *
 * 算法原理: 采用滑动窗口RMS算法，实时更新有效值
 */
void solar_AC_RMS_Calc(void)
{
    // ========== 电网电压有效值计算 ==========
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vgrid_a, AdcRegs.ADCRESULT5);  // 电网A相电压RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vgrid_b, AdcRegs.ADCRESULT7);  // 电网B相电压RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vgrid_c, AdcRegs.ADCRESULT9);  // 电网C相电压RMS

    // ========== 逆变器输出电压有效值计算 ==========
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vinv_a, AdcRegs.ADCRESULT6);   // 逆变器A相输出电压RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vinv_b, AdcRegs.ADCRESULT4);   // 逆变器B相输出电压RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Vinv_c, AdcRegs.ADCRESULT15);  // 逆变器C相输出电压RMS

    // ========== 输出电流有效值计算 ==========
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Ia, AdcRegs.ADCRESULT14);      // A相输出电流RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Ib, AdcRegs.ADCRESULT12);      // B相输出电流RMS
    SAMPLE_AC_RMS_CALCULATE_MACRO(h.Ic, AdcRegs.ADCRESULT13);      // C相输出电流RMS
}

/**
 * @brief 微秒级快速保护检测函数
 *
 * 功能: 执行最高优先级的快速保护检测和PV电压调节
 * 调用时机: 50us中断中调用
 * 保护内容:
 * 1. 三相输出电流过流保护 (立即触发)
 * 2. PV电流过流保护 (立即触发)
 * 3. PV电压过低保护和自动调节
 * 4. 母线电压过高保护
 *
 * 特点: 无延时保护，确保系统安全
 */
void solar_Protector_us(void)
{
    // ========== 快速电流保护检测 ==========
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ia, h.Ia.out_instant);    // A相电流过流保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ib, h.Ib.out_instant);    // B相电流过流保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ic, h.Ic.out_instant);    // C相电流过流保护
    PROTECT_CURRENT_CHECK_MACRO(h.Protect_Ipv1, h.Ipv1.out_instant); // PV1电流过流保护
    PROTECT_CURRENT_CHECK_MACRO(h.Protect_Ipv2, h.Ipv2.out_instant); // PV2电流过流保护

    // ========== PV电压自动调节和保护 ==========
    if (h.state > HSTATE_SYNC) // 仅在同步完成后执行电压调节
    {
        int32_t PwmCap = ECap2Regs.CAP4; // 读取当前PWM占空比

        if (h.Vpv2.out_instant < 320) // PV2电压偏低，需要减小升压比以降低负载
        {
            PwmCap--; // 减小PWM占空比，降低升压比

            if (h.Vpv2.out_instant < 300) // PV2电压进一步降低
            {
                PwmCap--;                        // 加快调节速度
                if (h.Vbus < h.hBoost.vbus_base) // 母线电压也不足，系统功率不足
                {
                    // 可选择重置升压电路 (已注释)
                    //                    solar_boost_disable();
                    //                    PwmCap = h.hBoost.pwm_base;

                    static uint32_t tick = 0;

                    // 每1秒发送一次调试信息
                    if (SOLAR_GET_TICK_COUNT(tick) > 1000)
                    {
                        tick = SOLAR_GET_TICK();
                        solar_send_msg("reset boost!"); // 发送升压重置消息
                    }
                }

                if (h.Vpv2.out_instant < 280) // PV2电压严重不足
                {
                    PwmCap -= 10; // 大幅减小PWM占空比

                    // 检查是否需要触发功率不足保护
                    if (h.Vbus < h.hBoost.vbus_base) // 母线电压低于基准值，触发保护
                    {
                        protect_set_error_code(PROT_PV_underpower, "PROT_PV_underpower");
                        h.Ctrl_Power_SW = 0; // 关闭功率开关
                        protect_reset_error_code(PROT_PV_underpower);
                    }
                }
            }
        }
        else if (h.Vbus < h.hBoost.vbus_base) // PV电压正常但母线电压不足，增加升压比
        {
            PwmCap++; // 增大PWM占空比，提高升压比
        }
        else if (h.Vbus > 810) // 母线电压过高保护
        {
            PwmCap--; // 减小PWM占空比，降低升压比
        }

        // ========== PWM占空比限幅保护 ==========
        if (PwmCap > h.hBoost.MaxCap) // 上限保护
            PwmCap = h.hBoost.MaxCap;
        if (PwmCap < 0) // 下限保护
            PwmCap = 0;

        ECap2Regs.CAP4 = PwmCap; // 更新PWM占空比寄存器
    }
}

/**
 * @brief 毫秒级延时保护检测函数
 *
 * 功能: 执行带延时的保护检测，避免瞬时干扰误触发
 * 调用时机: 1ms中断中调用
 * 保护内容:
 * 1. 三相输出电流延时保护 (2ms延时)
 * 2. PV电流延时保护 (1ms延时)
 * 3. 电网频率保护 (180ms延时)
 * 4. 电网电压保护 (100ms延时)
 * 5. PV电压保护 (1ms延时)
 * 6. 母线电压保护 (10ms延时)
 *
 * 特点: 有延时保护，提高系统稳定性
 */
void solar_Protector_ms(void)
{
    // ========== 电流延时保护检测 ==========
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ia_II, h.Ia.out_instant);     // A相电流延时保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ib_II, h.Ib.out_instant);     // B相电流延时保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Ic_II, h.Ic.out_instant);     // C相电流延时保护
    PROTECT_CURRENT_CHECK_MACRO(h.Protect_Ipv1_II, h.Ipv1.out_instant); // PV1电流延时保护
    PROTECT_CURRENT_CHECK_MACRO(h.Protect_Ipv2_II, h.Ipv2.out_instant); // PV2电流延时保护

    // ========== 电网参数保护检测 ==========
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Grid_Freq, h.spll_freq);       // 电网频率保护 (49.5-50.5Hz)

    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Grid_Ua, h.Vgrid_a.out_rms_xp); // 电网A相电压保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Grid_Ub, h.Vgrid_b.out_rms_xp); // 电网B相电压保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Grid_Uc, h.Vgrid_c.out_rms_xp); // 电网C相电压保护

    // ========== PV直流参数保护检测 ==========
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Vpv1, h.Vpv1.out_instant);     // PV1电压保护 (270-700V)
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_Vpv2, h.Vpv2.out_instant);     // PV2电压保护 (270-700V)

    // ========== 母线电压保护检测 ==========
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_VBusBalance, h.Vbus_pos.out_instant - h.Vbus_neg.out_instant); // 母线不平衡保护
    PROTECT_MEMBER_CHECK_MACRO(h.Protect_VBusSum, h.Vbus_pos.out_instant + h.Vbus_neg.out_instant);     // 母线总电压保护
}

/**
 * @brief 系统状态管理器
 *
 * 功能: 管理太阳能逆变器的运行状态转换
 * 调用时机: 1ms中断中调用
 * 状态转换流程:
 * START -> SYNC -> MERGE_ING -> RUNNING -> STOP
 *
 * 各状态说明:
 * - START: 启动状态，等待系统稳定
 * - SYNC: 同步状态，执行幅值同步
 * - MERGE_ING: 并网中状态，闭合并网接触器
 * - RUNNING: 运行状态，正常发电
 * - STOP: 停止状态，等待重启
 */
void solar_StateManager(void)
{
    switch (h.state)
    {
    case HSTATE_START: // 启动状态：检查系统稳定性，准备启动逆变器
    {
        static uint32_t NormalCount = 0, AbnormalCount = 0; // 正常和异常状态计数器

        if (h.hProtect.protect_word[0] == 0) // 无保护故障时
        {
            AbnormalCount = 0, NormalCount++; // 累计正常计数

            if (NormalCount >= 20000) // 连续20秒无故障，系统稳定
            {
                NormalCount = 0;

                solar_inv_pwm_enable(); // 使能逆变器PWM输出
                solar_boost_enable();   // 使能PV升压电路
                h.state = HSTATE_SYNC;  // 转入同步状态
            }
        }
        else // 存在保护故障时
        {
            NormalCount = 0, AbnormalCount++; // 累计异常计数

            if (AbnormalCount > 1000) // 连续1秒有故障
            {
                if ((h.spll_freq < -40.0f)) // 锁相环频率异常，可能是相序错误
                {
                    AbnormalCount = 0;
                    solar_off(); // 关闭系统
                    h.spll_phase_sequence = !(h.spll_phase_sequence); // 切换相序
                }
            }
        }

        break;
    }

    case HSTATE_SYNC: // 同步状态：等待幅值同步完成
    {
        static uint32_t DelayCount = 0;

        // 检查同步条件：幅值同步完成且无保护故障
        if (h.is_amp_sync_ok == 1 && h.hProtect.protect_word[0] == 0 && h.hProtect.protect_word[1] == 0)
        {
            DelayCount++;
            if (DelayCount >= 5000) // 同步完成后延时5秒确保稳定
            {
                DelayCount = 0;
                h.state = HSTATE_MERGE_ING; // 转入并网状态
            }
        }
        else
        {
            DelayCount = 0; // 条件不满足时重置计数器
        }

        break;
    }

    case HSTATE_MERGE_ING: // 并网中状态：闭合并网接触器
    {
        static uint16_t first = 1;

        if (first)
        {
            GpioDataRegs.GPCSET.bit.GPIO72 = 1; // 闭合并网接触器
            first = 0;
        }
        else
        {
            h.state = HSTATE_RUNING; // 转入运行状态
            first = 1; // 重置标志位
        }

        break;
    }

    case HSTATE_RUNING: // 运行状态：正常发电运行
    {
        // 在运行状态下，系统执行正常的发电控制
        // 包括MPPT算法、功率控制、保护监测等
        break;
    }

    case HSTATE_STOP: // 停止状态：等待重启命令
    {
        if (h.Ctrl_Power_SW) // 收到重启命令
        {
            solar_Init();           // 重新初始化系统
            h.state = HSTATE_START; // 转入启动状态
        }
        break;
    }

    default:
        break;
    }
}

/**
 * @brief 控制器参数初始化函数
 *
 * 功能: 初始化所有PI控制器的参数
 * 调用时机: 系统初始化时调用
 * 控制器类型:
 * 1. D轴电流控制器 - 控制有功电流
 * 2. Q轴电流控制器 - 控制无功电流
 * 3. 母线平衡控制器 - 控制正负母线平衡
 * 4. PV电压控制器 - 控制PV电压跟踪MPPT输出
 */
void solar_cntl_init(void)
{
    // ========== MPPT参考电压初始化 ==========
    h.Vpvref_mpptOut1 = 400; // PV1 MPPT输出参考电压 400V
    h.Vpvref_mpptOut2 = 400; // PV2 MPPT输出参考电压 400V

    // ========== D轴电流PI控制器初始化 ==========
    CNTL_PI_F_init(&h.hCNTL_PI_d);
    h.hCNTL_PI_d.Kp = 0.2;    // 比例增益 (控制响应速度)
    h.hCNTL_PI_d.Ki = 0.001;  // 积分增益 (消除稳态误差)
    h.hCNTL_PI_d.Umax = 0.1;  // 输出上限 (防止饱和)
    h.hCNTL_PI_d.Umin = -0.1; // 输出下限

    // ========== Q轴电流PI控制器初始化 ==========
    CNTL_PI_F_init(&h.hCNTL_PI_q);
    h.hCNTL_PI_q.Kp = 0.2;              // 比例增益
    h.hCNTL_PI_q.Ki = 0.1;              // 积分增益 (比D轴更大，快速响应)
    h.hCNTL_PI_q.Umax = 0.1 + 0.037;    // 输出上限 (含偏置量)
    h.hCNTL_PI_q.Umin = -0.1 + 0.037;   // 输出下限 (含偏置量)

    // ========== 母线平衡PI控制器初始化 ==========
    CNTL_PI_F_init(&h.hCNTL_PI_BusBalance);
    h.hCNTL_PI_BusBalance.Kp = 1;        // 比例增益 (快速响应不平衡)
    h.hCNTL_PI_BusBalance.Ki = 0.0001;   // 积分增益 (小积分避免振荡)
    h.hCNTL_PI_BusBalance.Umax = 0.5;    // 输出上限
    h.hCNTL_PI_BusBalance.Umin = -0.5;   // 输出下限
    h.hCNTL_PI_BusBalance.Ref = 0.0f;    // 参考值为0 (平衡状态)

    // ========== PV1电压PI控制器初始化 ==========
    CNTL_PI_F_init(&h.hCNTL_PI_Vpv1);
    h.hCNTL_PI_Vpv1.Kp = 0.02;          // 比例增益 (较小，避免振荡)
    h.hCNTL_PI_Vpv1.Ki = 0.01;          // 积分增益
    h.hCNTL_PI_Vpv1.Umax = 5.0;         // 输出上限 (PWM调节量)
    h.hCNTL_PI_Vpv1.Umin = -5.0;        // 输出下限
    h.hCNTL_PI_Vpv1.Ref = 400.0f;       // 参考电压 400V

    // ========== PV2电压PI控制器初始化 ==========
    CNTL_PI_F_init(&h.hCNTL_PI_Vpv2);
    h.hCNTL_PI_Vpv2.Kp = 0.2;           // 比例增益 (比PV1更大)
    h.hCNTL_PI_Vpv2.Ki = 0.02;          // 积分增益
    h.hCNTL_PI_Vpv2.Umax = 10.0;        // 输出上限 (更大调节范围)
    h.hCNTL_PI_Vpv2.Umin = -10.0;       // 输出下限
    h.hCNTL_PI_Vpv2.Ref = 400.0f;       // 参考电压 400V
}

/**
 * @brief 控制器计算函数
 *
 * 功能: 执行控制器计算 (当前为空函数，预留接口)
 * 调用时机: 根据需要调用
 * 说明: 此函数预留用于未来扩展控制算法
 */
void solar_cntl_calc(void)
{
    // 预留函数，用于未来扩展控制算法
}

/**
 * @brief MPPT算法初始化函数
 *
 * 功能: 初始化扰动观察法(P&O)MPPT算法参数
 * 调用时机: 系统初始化时调用
 * 算法原理: 通过扰动PV电压观察功率变化，寻找最大功率点
 *
 * 参数说明:
 * - DeltaPmin: 最小功率变化阈值
 * - MaxVolt/MinVolt: 电压搜索范围
 * - Stepsize: 电压扰动步长
 */
void solar_mppt_init(void)
{
    // ========== PV1 MPPT算法初始化 (当前未使用) ==========
    MPPT_PNO_F_init(&h.hMPPT_PNO1);
    h.hMPPT_PNO1.DeltaPmin = 0.00001;   // 最小功率变化阈值 (标准化值)
    h.hMPPT_PNO1.MaxVolt = 0.9;         // 最大电压 (标准化值)
    h.hMPPT_PNO1.MinVolt = 0.0;         // 最小电压 (标准化值)
    h.hMPPT_PNO1.Stepsize = 0.005;      // 电压扰动步长 (标准化值)

    // ========== PV2 MPPT算法初始化 (主要使用) ==========
    MPPT_PNO_F_init(&h.hMPPT_PNO2);
    h.hMPPT_PNO2.DeltaPmin = 5;         // 最小功率变化阈值 5W
    h.hMPPT_PNO2.MaxVolt = 380;         // 最大搜索电压 380V
    h.hMPPT_PNO2.MinVolt = 330;         // 最小搜索电压 330V
    h.hMPPT_PNO2.Stepsize = 6;          // 电压扰动步长 6V
}
/**
 * @brief MPPT算法计算函数
 *
 * 功能: 执行扰动观察法MPPT算法，寻找最大功率点
 * 调用时机: 在运行状态下每1ms调用
 * 算法流程:
 * 1. 每1秒执行一次MPPT算法，更新电压参考值
 * 2. 每50ms执行一次PV电压PI控制，调节PWM占空比
 *
 * 工作原理:
 * - 通过扰动PV电压，观察功率变化方向
 * - 向功率增加的方向继续扰动
 * - 最终收敛到最大功率点
 */
void solar_mppt_calc(void)
{
    static uint16_t count = 0; // 时间计数器

    // ========== MPPT算法执行 (每1秒执行一次) ==========
    if (count >= 1000) // 1000ms = 1秒
    {
        count = 0;

        MPPT_PNO_F_MACRO(h.hMPPT_PNO2);                    // 执行PV2的MPPT算法
        h.hCNTL_PI_Vpv2.Ref = h.hMPPT_PNO2.VmppOut;       // 更新PV2电压控制器参考值

        h.Vpvref_mpptOut2 = h.hMPPT_PNO2.VmppOut;          // 保存MPPT输出电压
    }

    // ========== PV电压PI控制 (每50ms执行一次) ==========
    if (count % 50 == 0) // 50ms周期
    {
        h.hCNTL_PI_Vpv2.Fbk = h.hMPPT_PNO2.Vpv;           // 设置PV2电压反馈值

        CNTL_PI_F_MACRO(h.hCNTL_PI_Vpv2);                 // 执行PV2电压PI控制

        // 根据PI控制器输出调节PWM占空比
        int32_t PwmCap = ECap2Regs.CAP4 + (-h.hCNTL_PI_Vpv2.Out); // 负反馈控制

        // PWM占空比限幅保护
        if (PwmCap > h.hBoost.MaxCap)
            PwmCap = h.hBoost.MaxCap;
        if (PwmCap < 0)
            PwmCap = 0;

        ECap2Regs.CAP4 = PwmCap; // 更新PWM占空比寄存器
    }

    count++; // 计数器递增
}

/**
 * @brief 锁相环(SPLL)初始化函数
 *
 * 功能: 初始化三相锁相环和坐标变换模块
 * 调用时机: 系统初始化时调用
 * 主要模块:
 * 1. 电网电压锁相环 - 跟踪电网频率和相位
 * 2. 输出电流锁相环 - 跟踪输出电流相位
 * 3. ABC-DQ0坐标变换 - 三相到两相变换
 *
 * 锁相环参数:
 * - 基准频率: 50Hz
 * - 采样频率: 20kHz
 * - PI控制器参数: Kp=0.0047, Ki=-0.9906
 */
void solar_spll_init(void)
{
    // ========== 电网电压坐标变换初始化 ==========
    ABC_DQ0_POS_F_init(&h.hABC_DQ0_POS);  // 正序分量ABC-DQ0变换
    ABC_DQ0_NEG_F_init(&h.hABC_DQ0_NEG);  // 负序分量ABC-DQ0变换

    // ========== 电网电压锁相环初始化 ==========
    SPLL_3ph_DDSRF_F_init(GRID_FREQ,           // 电网基准频率 50Hz
                          1.0f / ISR_FREQUENCY, // 采样周期 50us
                          0.0046983841,         // PI控制器比例增益 Kp
                          -0.9906032318,        // PI控制器积分增益 Ki
                          &h.hSPLL_3ph_DDSRF);  // 锁相环句柄

    // ========== 锁相环状态变量初始化 ==========
    h.spll_theta = 0.0f;           // 锁相环相位角初值
    h.spll_freq = 50.0f;           // 锁相环频率初值 50Hz
    h.spll_phase_sequence = 1;     // 相序标志 (1=正序, 0=负序)

    // 相位补偿量 (当前设为0，可用于补偿系统延时)
    h.spll_theta_compensation = 0; // 相位补偿角度 (弧度)

    // ========== 输出电流坐标变换初始化 ==========
    ABC_DQ0_POS_F_init(&h.hIabc_dq0_pos); // 电流正序分量ABC-DQ0变换
    ABC_DQ0_NEG_F_init(&h.hIabc_dq0_neg); // 电流负序分量ABC-DQ0变换

    // ========== 输出电流锁相环初始化 ==========
    SPLL_3ph_DDSRF_F_init(GRID_FREQ,           // 基准频率 50Hz
                          1.0f / ISR_FREQUENCY, // 采样周期 50us
                          0.0046983841,         // PI控制器比例增益 Kp
                          -0.9906032318,        // PI控制器积分增益 Ki
                          &h.hIspll_3ph_ddsrf); // 电流锁相环句柄
}

/**
 * @brief 锁相环计算函数
 *
 * 功能: 执行三相锁相环算法，跟踪电网频率和相位
 * 调用时机: 50us中断中调用
 * 主要功能:
 * 1. 电网电压锁相环计算 - 获取电网同步信号
 * 2. 输出电流锁相环计算 - 获取电流相位信息
 * 3. DQ坐标变换 - 将三相量转换为两相量
 * 4. SVPWM调制 - 生成PWM控制信号
 *
 * 算法原理: 基于DDSRF(双同步参考坐标系)的三相锁相环
 */
void solar_spll_calc(void)
{
    // ========== 电网电压标准化系数计算 ==========
    float GridMeasK = (1.0f / (RATED_VOLTAGE * 1.414f)); // 电网电压标准化系数 (基准值=220V*√2)
    float GridMeas1; // 标准化后的A相电网电压
    float GridMeas2; // 标准化后的B相电网电压
    float GridMeas3; // 标准化后的C相电网电压

    // ========== 输出电流标准化系数计算 ==========
    float I_MeasK = (1.0f / (RATED_CURRENT * 1.414f)); // 输出电流标准化系数 (基准值=15.15A*√2)
    static float I_Meas1; // 标准化后的A相输出电流
    static float I_Meas2; // 标准化后的B相输出电流
    static float I_Meas3; // 标准化后的C相输出电流

    // ========== 相序处理和信号标准化 ==========
    if (h.spll_phase_sequence) // 正相序 (A-B-C)
    {
        GridMeas1 = h.Vgrid_a.out_instant * GridMeasK; // A相电网电压标准化
        GridMeas2 = h.Vgrid_b.out_instant * GridMeasK; // B相电网电压标准化
        GridMeas3 = h.Vgrid_c.out_instant * GridMeasK; // C相电网电压标准化

        I_Meas1 = h.Ia.out_instant * I_MeasK; // A相输出电流标准化
        I_Meas2 = h.Ib.out_instant * I_MeasK; // B相输出电流标准化
        I_Meas3 = h.Ic.out_instant * I_MeasK; // C相输出电流标准化
    }
    else // 负相序 (A-C-B)，用于相序错误时的自动纠正
    {
        GridMeas3 = h.Vgrid_a.out_instant * GridMeasK; // 交换相序
        GridMeas2 = h.Vgrid_b.out_instant * GridMeasK;
        GridMeas1 = h.Vgrid_c.out_instant * GridMeasK;

        I_Meas3 = h.Ia.out_instant * I_MeasK; // 交换相序
        I_Meas2 = h.Ib.out_instant * I_MeasK;
        I_Meas1 = h.Ic.out_instant * I_MeasK;
    }

    // ========== 三角函数计算 ==========
    float sin_theta; // 锁相环相位角的正弦值
    float cos_theta; // 锁相环相位角的余弦值
    sincos(h.spll_theta, &sin_theta, &cos_theta); // 同时计算sin和cos，提高效率

    // ========== 电网电压正序分量DQ变换 ==========
    h.hABC_DQ0_POS.a = GridMeas1;   // 设置A相电网电压
    h.hABC_DQ0_POS.b = GridMeas2;   // 设置B相电网电压
    h.hABC_DQ0_POS.c = GridMeas3;   // 设置C相电网电压
    h.hABC_DQ0_POS.sin = sin_theta; // 设置变换角度的正弦值
    h.hABC_DQ0_POS.cos = cos_theta; // 设置变换角度的余弦值
    ABC_DQ0_POS_F_MACRO(h.hABC_DQ0_POS); // 执行ABC到DQ0正序变换

    // ========== 电网电压负序分量DQ变换 ==========
    h.hABC_DQ0_NEG.a = GridMeas1;   // 设置A相电网电压
    h.hABC_DQ0_NEG.b = GridMeas2;   // 设置B相电网电压
    h.hABC_DQ0_NEG.c = GridMeas3;   // 设置C相电网电压
    h.hABC_DQ0_NEG.sin = sin_theta; // 设置变换角度的正弦值
    h.hABC_DQ0_NEG.cos = cos_theta; // 设置变换角度的余弦值
    ABC_DQ0_NEG_F_MACRO(h.hABC_DQ0_NEG); // 执行ABC到DQ0负序变换

    // ========== 电网电压锁相环计算 ==========
    h.hSPLL_3ph_DDSRF.d_p = h.hABC_DQ0_POS.d; // 正序d轴分量
    h.hSPLL_3ph_DDSRF.q_p = h.hABC_DQ0_POS.q; // 正序q轴分量
    h.hSPLL_3ph_DDSRF.d_n = h.hABC_DQ0_NEG.d; // 负序d轴分量
    h.hSPLL_3ph_DDSRF.q_n = h.hABC_DQ0_NEG.q; // 负序q轴分量
    sincos(h.spll_theta * 2.0f, &h.hSPLL_3ph_DDSRF.sin_2theta, &h.hSPLL_3ph_DDSRF.cos_2theta); // 计算2倍频三角函数
    SPLL_3ph_DDSRF_F_MACRO(h.hSPLL_3ph_DDSRF); // 执行三相DDSRF锁相环算法

    // ========== 锁相环输出更新 ==========
    h.spll_freq = h.spll_freq + (h.hSPLL_3ph_DDSRF.fo - h.spll_freq) * (1.0f / (ISR_FREQUENCY / GRID_FREQ)); // 频率低通滤波
    h.spll_theta = h.hSPLL_3ph_DDSRF.theta[1]; // 更新锁相环相位角

    // ========== 输出电流锁相环计算 (仅在并网状态下执行) ==========
    if (SOLAR_GET_INTERCONNECTION()) // 检查是否处于并网状态
    {
        // ========== 输出电流正序分量DQ变换 ==========
        h.hIabc_dq0_pos.a = I_Meas1; // 设置A相输出电流
        h.hIabc_dq0_pos.b = I_Meas2; // 设置B相输出电流
        h.hIabc_dq0_pos.c = I_Meas3; // 设置C相输出电流

        // 使用电流锁相环的相位角进行坐标变换 (独立于电网锁相环)
        sincos(h.hIspll_3ph_ddsrf.theta[1], &h.hIabc_dq0_pos.sin, &h.hIabc_dq0_pos.cos);
        ABC_DQ0_POS_F_MACRO(h.hIabc_dq0_pos); // 执行ABC到DQ0正序变换

        // ========== 输出电流负序分量DQ变换 ==========
        h.hIabc_dq0_neg.a = I_Meas1; // 设置A相输出电流
        h.hIabc_dq0_neg.b = I_Meas2; // 设置B相输出电流
        h.hIabc_dq0_neg.c = I_Meas3; // 设置C相输出电流
        h.hIabc_dq0_neg.sin = h.hIabc_dq0_pos.sin; // 使用相同的三角函数值
        h.hIabc_dq0_neg.cos = h.hIabc_dq0_pos.cos;
        ABC_DQ0_NEG_F_MACRO(h.hIabc_dq0_neg); // 执行ABC到DQ0负序变换

        // ========== 输出电流锁相环计算 ==========
        h.hIspll_3ph_ddsrf.d_p = h.hIabc_dq0_pos.d; // 电流正序d轴分量
        h.hIspll_3ph_ddsrf.q_p = h.hIabc_dq0_pos.q; // 电流正序q轴分量
        h.hIspll_3ph_ddsrf.d_n = h.hIabc_dq0_neg.d; // 电流负序d轴分量
        h.hIspll_3ph_ddsrf.q_n = h.hIabc_dq0_neg.q; // 电流负序q轴分量

        // 计算电流锁相环的2倍频三角函数
        sincos(h.hIspll_3ph_ddsrf.theta[1] * 2, &h.hIspll_3ph_ddsrf.sin_2theta, &h.hIspll_3ph_ddsrf.cos_2theta);
        SPLL_3ph_DDSRF_F_MACRO(h.hIspll_3ph_ddsrf); // 执行电流锁相环算法

        // ========== 电流信号低通滤波 ==========
        h.Id_lpf += (h.hIspll_3ph_ddsrf.d_p_decoupl_lpf - h.Id_lpf) * (1.0f / 4000.0f); // d轴电流低通滤波

        // ========== 母线平衡控制 (仅在有足够有功电流时执行) ==========
        if (h.hIspll_3ph_ddsrf.d_p_decoupl_lpf > 0.05) // 有功电流大于0.05标准化单位
        {
            // 计算母线不平衡度作为反馈信号
            h.hCNTL_PI_BusBalance.Fbk = -(h.Vbus_pos.out_instant - h.Vbus_neg.out_instant) / h.Vbus;
            CNTL_PI_F_MACRO(h.hCNTL_PI_BusBalance); // 执行母线平衡PI控制
        }
    }

    // ========== PFC控制算法 (已注释，预留功能) ==========
    // 功能: 功率因数校正控制，可用于改善电网功率因数
    // 当前版本未启用此功能

    // ========== SVPWM调制信号生成 ==========
    h.hDQ_SVPWM.ud = h.hSPLL_3ph_DDSRF.d_p_decoupl; // d轴调制信号 (与电网电压同相)
    h.hDQ_SVPWM.uq = h.hSPLL_3ph_DDSRF.d_p_decoupl * (0.058f + hdebug.cntl_value); // q轴调制信号 (相位超前90度)

    // 注释说明:
    // - ud分量控制有功功率输出
    // - uq分量控制无功功率输出和功率因数
    // - 0.058f是基础相移系数，hdebug.cntl_value用于调试微调

    // ========== 相位角切换逻辑 (已注释，预留功能) ==========
    // 功能: 在电流足够大时，可以从电网锁相环切换到电流锁相环
    // 目的: 提高并网同步精度，当前版本使用电网锁相环

    // ========== SVPWM变换角度设置 ==========
    {
        h.hDQ_SVPWM.sin_th = sin_theta; // 设置SVPWM变换的正弦值
        h.hDQ_SVPWM.cos_th = cos_theta; // 设置SVPWM变换的余弦值
    }

    // ========== 执行SVPWM算法 ==========
    DQ_SVPWM_MACRO(h.hDQ_SVPWM); // 将DQ轴电压转换为三相PWM占空比

    // ========== 母线平衡补偿 (已注释，可选功能) ==========
    // 功能: 在PWM输出中加入母线平衡补偿量
    // 目的: 进一步改善正负母线电压平衡
    // 当前版本通过独立的母线平衡控制器实现
}

/**
 * @brief PWM波形输出控制函数
 *
 * 功能: 将SVPWM算法计算的调制信号转换为实际的PWM寄存器值
 * 调用时机: 50us中断中调用
 *
 * 工作原理:
 * 1. 将标准化的SVPWM输出转换为PWM计数器比较值
 * 2. 根据相序设置正确的相位映射关系
 * 3. 实现互补PWM输出，确保上下桥臂不会同时导通
 *
 * PWM计算公式:
 * - PWM计数值 = SVPWM输出 × 载波系数 × 调制深度
 * - 载波系数 = 2 × 1875 (对应20kHz载波频率)
 * - 调制深度 = 0.9 (90%调制深度，留10%死区时间)
 *
 * 硬件映射:
 * - EPwm1/2: A相上下桥臂 (U相)
 * - EPwm3/4: B相上下桥臂 (V相)
 * - EPwm5/6: C相上下桥臂 (W相)
 */
void solar_output_epwm(void)
{
    // ========== PWM输出使能检查 ==========
    if (h.is_output_epwm_enable == 0) // PWM输出被禁用时
    {
        // 清零所有PWM比较寄存器，确保输出为0
        EPwm1Regs.CMPA.half.CMPA = 0, EPwm2Regs.CMPA.half.CMPA = 0; // A相上下桥臂
        EPwm3Regs.CMPA.half.CMPA = 0, EPwm4Regs.CMPA.half.CMPA = 0; // B相上下桥臂
        EPwm5Regs.CMPA.half.CMPA = 0, EPwm6Regs.CMPA.half.CMPA = 0; // C相上下桥臂
        return;
    }

    int16_t epwm_a, epwm_b, epwm_c; // 三相PWM计数值

    // ========== PWM载波系数定义 ==========
    // PWM计数器最大值 = 1875 (对应20kHz载波频率)
    // 调制深度 = 0.9 (90%调制深度，保留10%用于死区时间)
    // 系数2.0是因为PWM计数器工作在上下计数模式
#define EPWM_K (2.0f * 1875.0f * CARRIER_MODULATION_RATIO)

    // ========== 相序处理和PWM值计算 ==========
    if (h.spll_phase_sequence) // 正相序 (A-B-C)
    {
        epwm_a = h.hDQ_SVPWM.pwma * EPWM_K; // A相PWM值 = SVPWM输出A × 载波系数
        epwm_b = h.hDQ_SVPWM.pwmb * EPWM_K; // B相PWM值 = SVPWM输出B × 载波系数
        epwm_c = h.hDQ_SVPWM.pwmc * EPWM_K; // C相PWM值 = SVPWM输出C × 载波系数
    }
    else // 负相序 (A-C-B)，用于相序错误时的自动纠正
    {
        epwm_c = h.hDQ_SVPWM.pwma * EPWM_K; // 交换相序：C相使用SVPWM的A相输出
        epwm_b = h.hDQ_SVPWM.pwmb * EPWM_K; // B相保持不变
        epwm_a = h.hDQ_SVPWM.pwmc * EPWM_K; // A相使用SVPWM的C相输出
    }

    // ========== 电流增益校正 (已注释，预留功能) ==========
    // 功能: 用于补偿三相电流传感器的增益差异
    // 目的: 提高三相电流的对称性和测量精度
    //    epwm_a *= h.Ia_gain; // A相电流增益校正
    //    epwm_b *= h.Ib_gain; // B相电流增益校正
    //    epwm_c *= h.Ic_gain; // C相电流增益校正

    // ========== 互补PWM输出宏定义 ==========
    // 功能: 根据PWM值的正负，控制上下桥臂的导通
    // 原理: 正值时上桥臂导通，负值时下桥臂导通，确保不会同时导通
#define SET_EPWM(pos, neg, val)     \
    {                               \
        if ((val) > 0) /* 正半周期 */ \
        {                           \
            (neg) = 0;              /* 下桥臂关断 */ \
            (pos) = (val);          /* 上桥臂按比例导通 */ \
        }                           \
        else /* 负半周期 */           \
        {                           \
            (pos) = 0;              /* 上桥臂关断 */ \
            (neg) = -(val);         /* 下桥臂按比例导通 */ \
        }                           \
    }

    // ========== 设置三相PWM寄存器值 ==========
    SET_EPWM(EPwm1Regs.CMPA.half.CMPA, EPwm2Regs.CMPA.half.CMPA, epwm_a); // A相上下桥臂PWM设置
    SET_EPWM(EPwm3Regs.CMPA.half.CMPA, EPwm4Regs.CMPA.half.CMPA, epwm_b); // B相上下桥臂PWM设置
    SET_EPWM(EPwm5Regs.CMPA.half.CMPA, EPwm6Regs.CMPA.half.CMPA, epwm_c); // C相上下桥臂PWM设置
}

/**
 * @brief 太阳能逆变器系统关闭函数
 *
 * 功能: 安全关闭整个太阳能逆变器系统
 * 调用时机: 系统故障、紧急停机或正常关机时
 *
 * 关闭顺序 (按安全优先级排序):
 * 1. 禁用PV升压电路 - 切断直流输入功率
 * 2. 禁用逆变器PWM输出 - 停止交流输出
 * 3. 断开并网接触器 - 与电网物理隔离
 * 4. 系统软复位 - 清除所有状态和参数
 *
 * 安全特性:
 * - 按照电力电子系统安全关闭顺序执行
 * - 确保不会产生电弧或过电压
 * - 为下次启动做好准备
 */
void solar_off(void)
{
    solar_boost_disable();    // 第一步：禁用PV升压电路，切断直流功率输入

    solar_inv_pwm_disable();  // 第二步：禁用逆变器PWM输出，停止交流功率输出

    GpioDataRegs.GPCCLEAR.bit.GPIO72 = 1; // 第三步：断开并网接触器，与电网物理隔离

    solar_reset();            // 第四步：系统软复位，清除所有控制状态
}

/**
 * @brief PV升压电路使能函数
 *
 * 功能: 启动PV升压电路，开始从太阳能板提取功率
 * 调用时机: 系统启动进入同步状态时
 *
 * 启动顺序:
 * 1. 清零PWM占空比寄存器 - 确保软启动
 * 2. 使能升压电路硬件 - 闭合升压电路继电器
 * 3. 设置软件使能标志 - 允许MPPT算法控制
 *
 * 硬件控制:
 * - ECap1/2.CAP4: PV1/PV2升压电路PWM占空比寄存器
 * - GPIO75: 升压电路使能继电器控制信号 (低电平有效)
 *
 * 安全特性:
 * - 软启动：PWM占空比从0开始，避免启动冲击
 * - 硬件保护：通过继电器物理隔离升压电路
 */
void solar_boost_enable(void)
{
    ECap1Regs.CAP4 = 0; // 清零PV1升压电路PWM占空比，确保软启动
    ECap2Regs.CAP4 = 0; // 清零PV2升压电路PWM占空比，确保软启动

    // 使能升压电路硬件 (GPIO75低电平有效，闭合升压电路继电器)
    GpioDataRegs.GPCCLEAR.bit.GPIO75 = 1;

    // 设置软件使能标志，允许MPPT算法和电压控制器工作
    h.hBoost.is_enable = 1;
}

/**
 * @brief PV升压电路禁用函数
 *
 * 功能: 安全关闭PV升压电路，停止功率提取
 * 调用时机: 系统故障、停机或保护触发时
 *
 * 关闭顺序:
 * 1. 禁用升压电路硬件 - 断开升压电路继电器
 * 2. 清除软件使能标志 - 停止MPPT算法控制
 * 3. 清零PWM占空比寄存器 - 确保完全关闭
 *
 * 硬件控制:
 * - GPIO75: 升压电路禁用继电器控制信号 (高电平禁用)
 * - ECap1/2.CAP4: 清零PWM占空比，停止开关动作
 *
 * 安全特性:
 * - 硬件优先：先断开继电器，再清除软件状态
 * - 完全关闭：确保所有PWM输出为0
 */
void solar_boost_disable(void)
{
    // 禁用升压电路硬件 (GPIO75高电平，断开升压电路继电器)
    GpioDataRegs.GPCSET.bit.GPIO75 = 1;

    // 清除软件使能标志，停止MPPT算法和电压控制器工作
    h.hBoost.is_enable = 0;
    ECap1Regs.CAP4 = 0; // 清零PV1升压电路PWM占空比
    ECap2Regs.CAP4 = 0; // 清零PV2升压电路PWM占空比
}
/**
 * @brief 逆变器PWM输出使能函数
 *
 * 功能: 启动逆变器PWM输出，开始产生交流电压
 * 调用时机: 系统启动状态检查通过后
 *
 * 启动顺序:
 * 1. 设置软件使能标志 - 允许PWM输出函数工作
 * 2. 释放PWM强制输出控制 - 恢复正常PWM波形
 * 3. 使能逆变器驱动电路 - 激活功率开关管驱动
 *
 * PWM强制输出控制说明:
 * - CSFB: 强制PWM B通道输出 (下桥臂)
 * - CSFA: 强制PWM A通道输出 (上桥臂)
 * - 0x0: 释放强制控制，恢复正常PWM输出
 *
 * 硬件控制:
 * - EPwm1-6: 三相六路PWM输出通道
 * - GPIO17: 逆变器驱动电路使能信号 (高电平有效)
 *
 * 安全特性:
 * - 分步启动：先释放下桥臂，延时后释放上桥臂
 * - 死区保护：确保上下桥臂不会同时导通
 */
void solar_inv_pwm_enable(void)
{
    // 设置软件使能标志，允许PWM输出函数工作
    h.is_output_epwm_enable = 1;

    // ========== 第一步：释放下桥臂PWM强制输出控制 ==========
    EPwm1Regs.AQCSFRC.bit.CSFB = 0x0; // 释放A相下桥臂强制输出
    EPwm2Regs.AQCSFRC.bit.CSFB = 0x0; // 释放A相下桥臂强制输出
    EPwm3Regs.AQCSFRC.bit.CSFB = 0x0; // 释放B相下桥臂强制输出
    EPwm4Regs.AQCSFRC.bit.CSFB = 0x0; // 释放B相下桥臂强制输出
    EPwm5Regs.AQCSFRC.bit.CSFB = 0x0; // 释放C相下桥臂强制输出
    EPwm6Regs.AQCSFRC.bit.CSFB = 0x0; // 释放C相下桥臂强制输出

    extern void DSP28x_usDelay(Uint32 Count);
    DELAY_US(10); // 延时10us，确保下桥臂状态稳定后再释放上桥臂

    // ========== 第二步：释放上桥臂PWM强制输出控制 ==========
    EPwm1Regs.AQCSFRC.bit.CSFA = 0x0; // 释放A相上桥臂强制输出
    EPwm2Regs.AQCSFRC.bit.CSFA = 0x0; // 释放A相上桥臂强制输出
    EPwm3Regs.AQCSFRC.bit.CSFA = 0x0; // 释放B相上桥臂强制输出
    EPwm4Regs.AQCSFRC.bit.CSFA = 0x0; // 释放B相上桥臂强制输出
    EPwm5Regs.AQCSFRC.bit.CSFA = 0x0; // 释放C相上桥臂强制输出
    EPwm6Regs.AQCSFRC.bit.CSFA = 0x0; // 释放C相上桥臂强制输出

    // ========== 第三步：使能逆变器驱动电路 ==========
    GpioDataRegs.GPASET.bit.GPIO17 = 1; // 使能逆变器驱动电路 (高电平有效)
}
/**
 * @brief 逆变器PWM输出禁用函数
 *
 * 功能: 安全关闭逆变器PWM输出，停止交流电压产生
 * 调用时机: 系统故障、停机或保护触发时
 *
 * 关闭顺序:
 * 1. 禁用逆变器驱动电路 - 切断功率开关管驱动信号
 * 2. 强制PWM输出到安全状态 - 确保所有开关管关闭
 * 3. 清除软件使能标志 - 停止PWM输出函数工作
 * 4. 清零PWM比较寄存器 - 确保完全关闭
 *
 * PWM强制输出控制说明:
 * - CSFA = 0x1: 强制上桥臂PWM输出低电平 (关闭上桥臂)
 * - CSFB = 0x2: 强制下桥臂PWM输出低电平 (关闭下桥臂)
 *
 * 硬件控制:
 * - GPIO17: 逆变器驱动电路禁用信号 (低电平禁用)
 * - EPwm1-6: 强制所有PWM通道输出低电平
 *
 * 安全特性:
 * - 硬件优先：先禁用驱动电路，再强制PWM状态
 * - 分步关闭：先关闭上桥臂，延时后关闭下桥臂
 * - 完全关闭：确保所有开关管都处于关闭状态
 */
void solar_inv_pwm_disable(void)
{
    // ========== 第一步：禁用逆变器驱动电路 ==========
    GpioDataRegs.GPACLEAR.bit.GPIO17 = 1; // 禁用逆变器驱动电路 (低电平禁用)

    // ========== 第二步：强制上桥臂PWM输出低电平 ==========
    EPwm1Regs.AQCSFRC.bit.CSFA = 0x1; // 强制A相上桥臂输出低电平 (关闭)
    EPwm2Regs.AQCSFRC.bit.CSFA = 0x1; // 强制A相上桥臂输出低电平 (关闭)
    EPwm3Regs.AQCSFRC.bit.CSFA = 0x1; // 强制B相上桥臂输出低电平 (关闭)
    EPwm4Regs.AQCSFRC.bit.CSFA = 0x1; // 强制B相上桥臂输出低电平 (关闭)
    EPwm5Regs.AQCSFRC.bit.CSFA = 0x1; // 强制C相上桥臂输出低电平 (关闭)
    EPwm6Regs.AQCSFRC.bit.CSFA = 0x1; // 强制C相上桥臂输出低电平 (关闭)

    extern void DSP28x_usDelay(Uint32 Count);
    DELAY_US(10); // 延时10us，确保上桥臂完全关闭后再处理下桥臂

    // ========== 第三步：强制下桥臂PWM输出低电平 ==========
    EPwm1Regs.AQCSFRC.bit.CSFB = 0x2; // 强制A相下桥臂输出低电平 (关闭)
    EPwm2Regs.AQCSFRC.bit.CSFB = 0x2; // 强制A相下桥臂输出低电平 (关闭)
    EPwm3Regs.AQCSFRC.bit.CSFB = 0x2; // 强制B相下桥臂输出低电平 (关闭)
    EPwm4Regs.AQCSFRC.bit.CSFB = 0x2; // 强制B相下桥臂输出低电平 (关闭)
    EPwm5Regs.AQCSFRC.bit.CSFB = 0x2; // 强制C相下桥臂输出低电平 (关闭)
    EPwm6Regs.AQCSFRC.bit.CSFB = 0x2; // 强制C相下桥臂输出低电平 (关闭)

    // ========== 第四步：清除软件状态和寄存器 ==========
    h.is_output_epwm_enable = 0; // 清除软件使能标志，停止PWM输出函数工作

    // 清零所有PWM比较寄存器，确保完全关闭
    EPwm1Regs.CMPA.half.CMPA = 0, EPwm2Regs.CMPA.half.CMPA = 0; // A相上下桥臂
    EPwm3Regs.CMPA.half.CMPA = 0, EPwm4Regs.CMPA.half.CMPA = 0; // B相上下桥臂
    EPwm5Regs.CMPA.half.CMPA = 0, EPwm6Regs.CMPA.half.CMPA = 0; // C相上下桥臂
}

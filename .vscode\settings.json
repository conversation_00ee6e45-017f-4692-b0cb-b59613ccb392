{"files.associations": {"protect.h": "c", "ad.h": "c", "compare": "c", "solar.h": "c", "main.h": "c", "stdint.h": "c", "dsp28x_project.h": "c", "duty_table.h": "c", "beforemerge.h": "c", "smppt.h": "c", "adamlib.h": "c", "pvboost.h": "c", "u_cstring": "c", "sample.h": "c", "math.h": "c", "inverter.h": "c", "sci.h": "c", "solar_f.h": "c", "abc_dq0_pos_f.h": "c", "dq0_abc_f.h": "c", "spll_3ph_ddsrf_f.h": "c", "spll_3ph_srf_f.h": "c", "spll_1ph_sogi_f.h": "c", "comm.h": "c", "config.h": "c", "pid_grando_f.h": "c", "cntl_2p2z_f.h": "c", "dlog_1ch_f.h": "c", "dlog_4ch_f.h": "c", "windows.h": "c", "clarke_f.h": "c", "string.h": "c", "dsp2833x_device.h": "c", "stdlib.h": "c", "type_traits": "c", "stdio.h": "c", "cntl_3p3z_f.h": "c", "svpwm_table.h": "c", "dq_svpwm.h": "c", "cstdarg": "c", "stdarg.h": "c", "common.h": "c", "c28x_fpu_fastrts.h": "c", "sc_master.h": "c", "dsp2833x_examples.h": "c"}}
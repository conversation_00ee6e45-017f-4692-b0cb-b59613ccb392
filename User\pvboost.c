/*
 * pvboost.c - PV升压电路控制模块
 *
 * 功能描述:
 * 本模块负责太阳能光伏(PV)升压电路的控制和管理
 * 主要功能包括升压电路初始化、功率控制、幅值同步等
 *
 * 升压电路作用:
 * 1. 将PV电压升压到合适的直流母线电压
 * 2. 实现最大功率点跟踪(MPPT)功能
 * 3. 为逆变器提供稳定的直流电源
 *
 * 创建时间: 2024年5月23日
 * 作者: ZH
 * 模块: 升压电路控制器
 */

#include "pvboost.h"
#include "solar.h"

/**
 * @brief PV升压电路控制器初始化函数
 * @param PVB PV升压控制器句柄指针
 *
 * 功能: 初始化PV升压电路控制器的所有参数
 * 调用时机: 系统启动时调用
 *
 * 初始化参数说明:
 * - amp_err_min: 幅值同步误差最小阈值
 * - MaxCap: PWM占空比最大值限制
 * - power_target: 目标功率设定值
 * - power_ctlr: 功率控制器当前值
 *
 * 安全特性:
 * - 清零所有控制状态，确保安全启动
 * - 设置合理的功率和PWM限制值
 */
void PVBoost_Init(PVBoost_Handle_t *PVB)
{
    // 清零升压控制器句柄，确保初始状态安全
    memset(PVB, 0x00, sizeof(PVBoost_Handle_t));

    // ========== 控制参数初始化 ==========
    //    PVB->DeltaVBusmin = 1;          // 母线电压变化最小阈值 (已注释，预留功能)
    PVB->amp_err_min = 1;                // 幅值同步误差最小阈值 1V
    PVB->MaxCap = 6000;                  // PWM占空比最大值 6000 (对应最大升压比)
    PVB->power_target = 5000;            // 目标功率 5000W (系统额定功率的一半)
    PVB->power_ctlr = 200;               // 功率控制器初始值 200W (软启动功率)
}

# 项目上下文信息

- 10KW太阳能逆变器项目关键组件分析完成：
1. LOCK机制包括PLL硬件锁定、CSM安全锁定、中断锁定和软件PLL锁定四种类型
2. GPIO17配置为LOCK状态指示输出引脚，输出高电平表示系统正常锁定状态
3. 主要文件：main.c(主程序)、DSP2833x_SysCtrl.c(系统控制)、SPLL相关文件(软件锁相环)
4. 系统基于TMS320F28335 DSP，采用多层次安全保护机制
- GPIO17(LOCK)功能分析完成：
1. GPIO17不是PWM的直接总开关，而是状态指示信号
2. 真正的PWM控制通过PWMOutputsEnable/Disable函数实现
3. GPIO17通过M_INVPWM_UNLOCK/LOCK宏定义控制高低电平
4. 高电平表示解锁状态，低电平表示锁定状态
5. 与PWM控制、继电器控制形成多层次安全保护机制
- 10KW光伏逆变器LOCK信号分析完成：
1. GPIO17当前初始化为高电平(解锁状态)，位于main.c第68行
2. 电平逻辑：高电平=解锁，低电平=锁定，宏定义在SC_define.h
3. 主要使用位置：solar_inv_pwm_enable/disable函数、PWMOFFandRelayDisconnect函数、SystemInitial函数
4. 存在两套PWM控制体系：旧版PWMOutputsEnable/Disable和新版solar_inv_pwm_enable/disable
5. 修改为低电平初始化需要同步修改PWMOutputsEnable调用时序，确保先解锁再使能PWM
- 10KW光伏逆变器LOCK信号完整分析已完成：1. GPIO17当前高电平初始化，存在双重PWM控制体系冲突 2. 新版solar_inv_pwm_enable/disable包含完整LOCK控制逻辑，旧版PWMOutputsEnable不操作LOCK 3. 推荐方案A保守修改：LOCK低电平初始化+注释旧版PWM调用 4. 已生成完整技术分析报告LOCK信号分析报告.md，包含风险评估和测试计划
- 10KW太阳能逆变器三相PWM控制系统分析完成：1. 三相PWM不是由单一引脚控制，而是6路独立PWM信号(EPwm1-6)控制三相上下桥臂 2. GPIO17是逆变器驱动电路的总使能信号，控制所有PWM驱动器的电源 3. 每相有2路PWM：上桥臂(EPwmxA)和下桥臂(EPwmxB)，通过互补控制和死区保护确保安全 4. 主要控制函数：solar_inv_pwm_enable/disable、solar_output_epwm、InitEPwm 5. PWM频率20kHz，死区时间3.2μs，采用SVPWM调制算法
- 10KW太阳能逆变器三相PWM控制系统完整分析已完成：1. 核心控制函数：solar_output_epwm()、DQ_SVPWM_MACRO()、solar_spll_calc() 2. PWM配置：20kHz频率、90%调制深度、3.2μs死区时间 3. GPIO映射：EPwm1-6对应GPIO0-11控制三相上下桥臂 4. SVPWM算法：DQ反变换→扇区判断→矢量合成→PWM生成 5. 安全保护：GPIO17总使能、死区保护、强制输出控制 6. 关键文件：User/dq_svpwm.h/c(SVPWM算法)、source/DSP/DSP2833x_EPwm.c(PWM初始化)、User/solar.c(控制逻辑)
- 太阳能逆变器同步状态详解已完成：1. 同步状态(HSTATE_SYNC)是并网前的关键准备阶段，确保逆变器输出与电网电压在幅值和相位上匹配 2. 幅值同步通过solar_amp_sync()函数实现，自动调节PWM占空比使输出电压接近电网电压 3. 相位同步通过锁相环(SPLL)和DQ坐标变换实现 4. 同步完成判断：连续32次电压误差在允许范围内 5. 状态转换：START→SYNC→MERGE_ING→RUNING 6. 关键标志位：is_amp_sync_ok 7. 安全保护：电网电压>132V、母线电压<810V、PWM占空比限制
- GPIO75功能确认：GPIO75是PV升压电路继电器控制信号，低电平有效闭合继电器使能升压电路，高电平断开继电器禁用升压电路。主要控制函数：solar_boost_enable()和solar_boost_disable()。与ECap1/ECap2.CAP4寄存器配合控制PV1/PV2升压电路PWM占空比。初始化时配置为调试监控引脚但实际用于升压电路控制。
- GPIO迁移分析完成：GPIO75当前用于PV升压电路继电器控制，与PV电压采样电路冲突需迁移到GPIO44。GPIO44/74/76都属于XINTF外部接口但项目未使用XINTF功能，这些GPIO都可安全用于普通功能。主要修改位置：main.c的Gpio_setup()函数、User/solar.c的solar_boost_enable/disable函数。需要注意寄存器组差异：GPIO75属于GPC组，GPIO44属于GPB组。
- GPIO74/75/76三相PWM开关控制方案分析完成：1. GPIO74/75/76无法直接配置为PWM输出，但可作为三相PWM开关控制信号 2. 实现方案：在solar_output_epwm()函数中读取GPIO状态，根据高低电平控制对应相PWM输出 3. GPIO75存在功能冲突，当前用于PV升压电路继电器控制，需迁移到GPIO44 4. 硬件要求：3.3V CMOS逻辑电平，建议添加RC滤波电路 5. 控制效果：每相独立控制，50us实时响应，支持紧急关闭和调试测试
- PWM开关控制时序分析完成：1. LOCK总开关(GPIO17)关闭时序已正确实现：先关LOCK再关PWM，符合安全要求 2. 开启时序支持同时开启LOCK和三相开关 3. 存在双重PWM控制体系：旧版PWMOutputsEnable和新版solar_inv_pwm_enable 4. GPIO74/76可用于三相开关，GPIO75被PV升压继电器占用需迁移 5. 当前缺少GPIO74/75/76三相独立开关控制逻辑实现
- GPIO77/78/79功能分析完成：1. GPIO77/78/79在SC_define.h中定义为CHOICE_KIND2/3/4_STATE宏，但项目中没有实际使用代码 2. 这些GPIO只是预留功能，当前没有作为继电器控制实现 3. 实际的继电器控制引脚：GPIO72(并网接触器)和GPIO75(PV升压继电器) 4. GPIO77/78/79默认为输入模式，可能设计用于功率级选择检测或三相继电器控制预留 5. 当前可安全用于其他功能，不会影响现有系统
- PWM开关控制时序分析报告第三点实现方案确认：用户需要实现GPIO17(LOCK)作为总开关控制整个PWM系统，GPIO74/75/76分别作为A/B/C三相PWM输出的独立开关。实现方案是在solar_output_epwm()函数中添加GPIO状态检测，根据GPIO74/75/76的高低电平控制对应相的PWM输出（低电平时PWM输出为0，高电平时正常输出）。需要解决GPIO75与PV升压电路功能冲突问题。
- GPIO72功能分析完成：1. GPIO72确实用作控制ABC三相并网继电器的开关信号，高电平闭合继电器，低电平断开继电器 2. 初始化在main.c第314-319行，配置为输出模式，初始低电平 3. 控制逻辑在User/solar.c第868行(并网时置高)和第1361行(故障时置低) 4. 完全由状态机驱动：HSTATE_START→HSTATE_SYNC→HSTATE_MERGE_ING(GPIO72置高)→HSTATE_RUNING 5. 安全保护：solar_off()函数按顺序关闭升压电路→PWM→并网继电器→系统复位 6. 时序严格：20000次循环确认稳定→同步完成→5000次延时→并网，采用故障安全设计
- 10KW太阳能逆变器串口通信分析完成：1. 串口A(115200)用于调试，打印初始化信息、保护故障、电流数据、完整系统数据包和波形数据 2. 串口B(9600)用于Modbus RTU通信与电能表交互 3. 主要数据以二进制格式每秒发送，包含PV参数、母线电压、电网参数、输出参数等 4. 支持调试命令接收用于参数调整 5. 文本格式数据用于监控，二进制数据需专用上位机解析
- 太阳能逆变器相位调整教学完成：1. 已生成完整教程文档《太阳能逆变器相位调整完整教程.md》 2. 详细解释了4种相位调整方法：相位补偿调整(推荐新手)、锁相环PI参数调整、SVPWM相位微调、相序处理 3. 重点推荐调整User/solar.c第1083行的h.spll_theta_compensation参数 4. 提供了安全操作指南和故障排除方法 5. 用户表示要结束对话去学习文档内容
- SPLL_3ph_DDSRF_F锁相环库文件注释完成：已为ti_solar_lib\include\SPLL_3ph_DDSRF_F.h头文件和ti_solar_lib\source\SPLL_3ph_DDSRF_F.c实现文件添加了全面的中文注释，包括文件功能说明、结构体成员详解、函数参数说明、算法原理解释、数学公式推导、参数调整指导等，注释涵盖解耦网络、低通滤波器、环路滤波器、VCO等核心模块，便于理解三相双同步参考坐标系锁相环的工作原理和工程实现
- 用户询问过零点检测的具体算法实现，想了解代码中是如何计算过零点的，是否通过最大最小值计算。需要详细解释锁相环算法中过零点检测的数学原理和代码实现。
- MPPT算法教学完成：该项目包含两套MPPT算法实现：1. TI库算法(ti_solar_lib)：P&O算法(MPPT_PNO_F)、增量电导法(MPPT_INCC_F)、电流控制增量电导法(MPPT_INCC_I_F) 2. 自定义算法(SC_MPPT.c)：改进P&O算法，支持双路独立MPPT(MPPTTrack/MPPT1Track/MPPT2Track)，具有功率限制和动态步长调节功能。MPPT输出通过ECap1/ECap2的PWM占空比控制升压电路，实现最大功率点跟踪。
- 10KW太阳能逆变器并网功能完整分析已完成：1. 并网流程：5状态转换(STOP→START→SYNC→MERGE_ING→RUNNING)，20秒稳定检测→幅值同步→5秒延时→并网接触器闭合 2. 技术实现：SPLL_3ph_DDSRF锁相环、solar_amp_sync幅值同步、GPIO72并网继电器控制、SVPWM三相PWM输出 3. 并网条件：17项保护参数(电网频率49.5-50.5Hz、电压183-246V、母线电压276-830V等)，双级保护机制(50us快速+1ms延时) 4. 安全保护：solar_off()安全关闭序列、相序自动切换、功率不足保护、电网异常断网保护
- 用户是单片机初学者，正在学习太阳能逆变器项目中的保护机制，特别关注solar_Protector_us()这个50微秒级快速保护函数的具体实现。已完成保护系统整体架构分析，包括三层保护时序、保护数据结构、核心宏定义、保护初始化和错误代码架构。用户选择继续分析函数内部的详细保护逻辑。
- 10KW太阳能逆变器电网电压配置分析完成：1. 当前配置220V额定电压，保护范围183V-246V，同步阈值132V 2. 需要修改为190V需要调整5个关键位置：RATED_VOLTAGE宏定义、电网电压采样校准系数、同步检测阈值等 3. 修改后保护范围自动调整为158V-213V，190V在安全范围内 4. 主要文件：User/solar.c(第23、137-141、194-199、551、1113行) 5. 用户要求生成总结性Markdown文档，不执行实际修改

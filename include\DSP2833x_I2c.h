// TI File $Revision: /main/2 $
// Checkin $Date: March 22, 2007   10:40:22 $
//###########################################################################
//
// FILE:   DSP2833x_I2c.h
//
// TITLE:  DSP2833x Enhanced Quadrature Encoder Pulse Module
//         Register Bit Definitions.
//
//###########################################################################
// $TI Release: 2833x/2823x Header Files V1.32 $
// $Release Date: June 28, 2010 $
// $Copyright:
// Copyright (C) 2009-2021 Texas Instruments Incorporated - http://www.ti.com/
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//###########################################################################

#ifndef DSP2833x_I2C_H
#define DSP2833x_I2C_H


#ifdef __cplusplus
extern "C" {
#endif

//
// I2C interrupt vector register bit definitions
//
struct I2CISRC_BITS {          // bits   description
    Uint16 INTCODE:3;          // 2:0    Interrupt code
    Uint16 rsvd1:13;           // 15:3   reserved
};

union I2CISRC_REG {
    Uint16                 all;
    struct I2CISRC_BITS    bit;
};

//
// I2C interrupt mask register bit definitions
//
struct I2CIER_BITS {           // bits   description
    Uint16 ARBL:1;             // 0      Arbitration lost interrupt
    Uint16 NACK:1;             // 1      No ack interrupt
    Uint16 ARDY:1;             // 2      Register access ready interrupt
    Uint16 RRDY:1;             // 3      Recieve data ready interrupt
    Uint16 XRDY:1;             // 4      Transmit data ready interrupt
    Uint16 SCD:1;              // 5      Stop condition detection
    Uint16 AAS:1;              // 6      Address as slave
    Uint16 rsvd:9;             // 15:7   reserved
};

union I2CIER_REG {
    Uint16                 all;
    struct I2CIER_BITS     bit;
};

//
// I2C status register bit definitions
//
struct I2CSTR_BITS {           // bits   description
    Uint16 ARBL:1;             // 0      Arbitration lost interrupt
    Uint16 NACK:1;             // 1      No ack interrupt
    Uint16 ARDY:1;             // 2      Register access ready interrupt
    Uint16 RRDY:1;             // 3      Recieve data ready interrupt
    Uint16 XRDY:1;             // 4      Transmit data ready interrupt
    Uint16 SCD:1;              // 5      Stop condition detection
    Uint16 rsvd1:2;            // 7:6    reserved
    Uint16 AD0:1;              // 8      Address Zero
    Uint16 AAS:1;              // 9      Address as slave
    Uint16 XSMT:1;             // 10     XMIT shift empty
    Uint16 RSFULL:1;           // 11     Recieve shift full
    Uint16 BB:1;               // 12     Bus busy
    Uint16 NACKSNT:1;          // 13     A no ack sent
    Uint16 SDIR:1;             // 14     Slave direction
    Uint16 rsvd2:1;            // 15     reserved
};

union I2CSTR_REG {
    Uint16                 all;
    struct I2CSTR_BITS     bit;
};

//
// I2C mode control register bit definitions
//
struct I2CMDR_BITS {           // bits   description
    Uint16 BC:3;               // 2:0    Bit count
    Uint16 FDF:1;              // 3      Free data format
    Uint16 STB:1;              // 4      Start byte
    Uint16 IRS:1;              // 5      I2C Reset not
    Uint16 DLB:1;              // 6      Digital loopback
    Uint16 RM:1;               // 7      Repeat mode
    Uint16 XA:1;               // 8      Expand address
    Uint16 TRX:1;              // 9      Transmitter/reciever
    Uint16 MST:1;              // 10     Master/slave
    Uint16 STP:1;              // 11     Stop condition
    Uint16 rsvd1:1;            // 12     reserved
    Uint16 STT:1;              // 13     Start condition
    Uint16 FREE:1;             // 14     Emulation mode
    Uint16 NACKMOD:1;          // 15     No Ack mode
};

union I2CMDR_REG {
   Uint16                 all;
   struct I2CMDR_BITS     bit;
};

//
// I2C extended mode control register bit definitions
//
struct I2CEMDR_BITS {          // bits   description
    Uint16 BCM:1;              // 0      Backward compatibility mode
    Uint16 rsvd:15;            // 15     reserved   
};

union I2CEMDR_REG {
    Uint16                 all;
    struct I2CEMDR_BITS     bit;
};

//
// I2C pre-scaler register bit definitions
//
struct I2CPSC_BITS {          // bits   description
    Uint16 IPSC:8;            // 7:0    pre-scaler
    Uint16 rsvd1:8;           // 15:8   reserved
};

union I2CPSC_REG {
    Uint16                 all;
    struct I2CPSC_BITS     bit;
};

//
// TX FIFO control register bit definitions
//
struct I2CFFTX_BITS {          // bits   description
    Uint16 TXFFIL:5;           // 4:0    FIFO interrupt level
    Uint16 TXFFIENA:1;         // 5      FIFO interrupt enable/disable
    Uint16 TXFFINTCLR:1;       // 6      FIFO clear
    Uint16 TXFFINT:1;          // 7      FIFO interrupt flag
    Uint16 TXFFST:5;           // 12:8   FIFO level status
    Uint16 TXFFRST:1;          // 13     FIFO reset
    Uint16 I2CFFEN:1;          // 14     enable/disable TX & RX FIFOs
    Uint16 rsvd1:1;            // 15     reserved
};

union I2CFFTX_REG {
    Uint16                 all;
    struct I2CFFTX_BITS    bit;
};

//
// RX FIFO control register bit definitions
//
struct I2CFFRX_BITS {          // bits   description
    Uint16 RXFFIL:5;           // 4:0    FIFO interrupt level
    Uint16 RXFFIENA:1;         // 5      FIFO interrupt enable/disable
    Uint16 RXFFINTCLR:1;       // 6      FIFO clear
    Uint16 RXFFINT:1;          // 7      FIFO interrupt flag
    Uint16 RXFFST:5;           // 12:8   FIFO level
    Uint16 RXFFRST:1;          // 13     FIFO reset
    Uint16 rsvd1:2;            // 15:14  reserved
};

union I2CFFRX_REG {
    Uint16                 all;
    struct I2CFFRX_BITS    bit;
};

struct I2C_REGS {
    Uint16              I2COAR;             // Own address register
    union  I2CIER_REG   I2CIER;             // Interrupt enable
    union  I2CSTR_REG   I2CSTR;             // Interrupt status
    Uint16              I2CCLKL;            // Clock divider low
    Uint16              I2CCLKH;            // Clock divider high
    Uint16              I2CCNT;             // Data count
    Uint16              I2CDRR;             // Data recieve
    Uint16              I2CSAR;             // Slave address
    Uint16              I2CDXR;             // Data transmit
    union  I2CMDR_REG   I2CMDR;             // Mode
    union  I2CISRC_REG  I2CISRC;            // Interrupt source
    union  I2CEMDR_REG  I2CEMDR;            // Extended Mode   
    union  I2CPSC_REG   I2CPSC;             // Pre-scaler
    Uint16              rsvd2[19];          // reserved
    union  I2CFFTX_REG  I2CFFTX;            // Transmit FIFO
    union  I2CFFRX_REG  I2CFFRX;            // Recieve FIFO
};

//
// External References & Function Declarations
//
extern volatile struct I2C_REGS I2caRegs;

#ifdef __cplusplus
}
#endif /* extern "C" */

#endif  // end of DSP2833x_I2C_H definition

//
// End of file
//

